-- 监控平台数据库初始化脚本
-- 此脚本会在PostgreSQL容器启动时自动执行

-- 创建monitor schema
CREATE SCHEMA IF NOT EXISTS monitor;

-- 设置搜索路径
SET search_path TO monitor, public;

-- 创建告警记录表
-- 用于存储所有告警事件的记录，支持告警生命周期管理
CREATE TABLE IF NOT EXISTS monitor.alerts (
    id SERIAL PRIMARY KEY,                                                    -- 告警记录唯一标识
    rule_name VARCHAR(100) NOT NULL,                                         -- 触发告警的规则名称，对应配置文件中的规则
    service_name VARCHAR(100) NOT NULL,                                      -- 产生告警的服务名称
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'firing', 'resolved')), -- 告警状态：pending(待处理)、firing(告警中)、resolved(已解决)
    message TEXT NOT NULL,                                                   -- 告警详细消息内容
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'critical')), -- 告警严重级别：info(信息)、warning(警告)、critical(严重)
    started_at TIMESTAMPTZ DEFAULT NOW(),                                    -- 告警开始时间
    resolved_at TIMESTAMPTZ,                                                 -- 告警解决时间，NULL表示未解决
    metadata JSONB,                                                          -- 告警相关的元数据，JSON格式存储额外信息
    created_at TIMESTAMPTZ DEFAULT NOW(),                                    -- 记录创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW()                                     -- 记录最后更新时间
);

-- 为alerts表添加注释
COMMENT ON TABLE monitor.alerts IS '告警记录表，存储所有告警事件的完整生命周期信息';
COMMENT ON COLUMN monitor.alerts.id IS '告警记录唯一标识，自增主键';
COMMENT ON COLUMN monitor.alerts.rule_name IS '触发告警的规则名称，对应Nacos配置中的alert_rules';
COMMENT ON COLUMN monitor.alerts.service_name IS '产生告警的服务名称，用于关联具体的监控服务';
COMMENT ON COLUMN monitor.alerts.status IS '告警当前状态：pending(刚触发待确认)、firing(正在告警)、resolved(已解决)';
COMMENT ON COLUMN monitor.alerts.message IS '告警的详细描述信息，包含具体的错误或异常情况';
COMMENT ON COLUMN monitor.alerts.severity IS '告警严重程度：info(一般信息)、warning(需要关注)、critical(紧急处理)';
COMMENT ON COLUMN monitor.alerts.started_at IS '告警首次触发的时间戳';
COMMENT ON COLUMN monitor.alerts.resolved_at IS '告警解决的时间戳，NULL表示告警仍在进行中';
COMMENT ON COLUMN monitor.alerts.metadata IS '告警相关的扩展信息，JSON格式，如错误详情、服务端点等';
COMMENT ON COLUMN monitor.alerts.created_at IS '数据库记录创建时间';
COMMENT ON COLUMN monitor.alerts.updated_at IS '数据库记录最后更新时间，通过触发器自动维护';

-- 创建告警表索引
CREATE INDEX IF NOT EXISTS idx_alerts_service_status_time
ON monitor.alerts(service_name, status, started_at);

CREATE INDEX IF NOT EXISTS idx_alerts_rule_status_time
ON monitor.alerts(rule_name, status, started_at);

CREATE INDEX IF NOT EXISTS idx_alerts_status_started
ON monitor.alerts(status, started_at);

-- 创建通知记录表
-- 用于记录所有告警通知的发送历史和状态
CREATE TABLE IF NOT EXISTS monitor.notifications (
    id SERIAL PRIMARY KEY,                                                    -- 通知记录唯一标识
    alert_id INTEGER REFERENCES monitor.alerts(id) ON DELETE CASCADE,        -- 关联的告警记录ID，级联删除
    channel VARCHAR(50) NOT NULL CHECK (channel IN ('email', 'sms', 'webhook')), -- 通知渠道：email(邮件)、sms(短信)、webhook(钩子)
    recipient VARCHAR(200) NOT NULL,                                         -- 通知接收者，邮箱地址、手机号或webhook URL
    content TEXT,                                                            -- 发送的通知内容，记录实际发送给用户的消息内容
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'sent', 'failed')), -- 发送状态：pending(待发送)、sent(已发送)、failed(发送失败)
    sent_at TIMESTAMPTZ,                                                     -- 实际发送时间，NULL表示未发送
    error_message TEXT,                                                      -- 发送失败时的错误信息
    retry_count INTEGER DEFAULT 0,                                           -- 重试次数，用于失败重发控制
    created_at TIMESTAMPTZ DEFAULT NOW(),                                    -- 记录创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW()                                     -- 记录最后更新时间
);

-- 为notifications表添加注释
COMMENT ON TABLE monitor.notifications IS '通知记录表，存储所有告警通知的发送历史和状态跟踪';
COMMENT ON COLUMN monitor.notifications.id IS '通知记录唯一标识，自增主键';
COMMENT ON COLUMN monitor.notifications.alert_id IS '关联的告警记录ID，外键引用alerts表';
COMMENT ON COLUMN monitor.notifications.channel IS '通知发送渠道：email(邮件)、sms(短信)、webhook(HTTP回调)';
COMMENT ON COLUMN monitor.notifications.recipient IS '通知接收者标识，根据channel类型可能是邮箱、手机号或URL';
COMMENT ON COLUMN monitor.notifications.content IS '发送的通知内容，记录实际发送给用户的消息内容';
COMMENT ON COLUMN monitor.notifications.status IS '通知发送状态：pending(排队中)、sent(发送成功)、failed(发送失败)';
COMMENT ON COLUMN monitor.notifications.sent_at IS '通知实际发送完成的时间戳';
COMMENT ON COLUMN monitor.notifications.error_message IS '发送失败时记录的详细错误信息';
COMMENT ON COLUMN monitor.notifications.retry_count IS '失败重试次数，用于控制重发策略';
COMMENT ON COLUMN monitor.notifications.created_at IS '数据库记录创建时间';
COMMENT ON COLUMN monitor.notifications.updated_at IS '数据库记录最后更新时间';

-- 创建通知表索引
CREATE INDEX IF NOT EXISTS idx_notifications_alert_status
ON monitor.notifications(alert_id, status);

CREATE INDEX IF NOT EXISTS idx_notifications_status_created
ON monitor.notifications(status, created_at);

-- 创建监控指标历史表
-- 用于存储各种监控指标的历史数据，支持趋势分析和异常检测
CREATE TABLE IF NOT EXISTS monitor.metrics_history (
    id SERIAL PRIMARY KEY,                                                    -- 指标记录唯一标识
    service_name VARCHAR(100) NOT NULL,                                      -- 服务名称，标识指标来源
    metric_name VARCHAR(100) NOT NULL,                                       -- 指标名称，如connections、message_rate、cpu_usage等
    metric_value DECIMAL(15,2) NOT NULL,                                     -- 指标数值，支持小数点后2位精度
    tags JSONB,                                                              -- 指标标签，JSON格式存储维度信息（如消费者组、主题、分区等）
    collected_at TIMESTAMPTZ DEFAULT NOW(),                                  -- 指标采集时间
    created_at TIMESTAMPTZ DEFAULT NOW()                                     -- 记录创建时间
);

-- 为metrics_history表添加注释
COMMENT ON TABLE monitor.metrics_history IS '监控指标历史表，存储各服务的性能指标时序数据，使用硬删除策略';
COMMENT ON COLUMN monitor.metrics_history.id IS '指标记录唯一标识，自增主键';
COMMENT ON COLUMN monitor.metrics_history.service_name IS '产生指标的服务名称，用于区分不同的监控目标';
COMMENT ON COLUMN monitor.metrics_history.metric_name IS '指标类型名称，如连接数、消息速率、CPU使用率等';
COMMENT ON COLUMN monitor.metrics_history.metric_value IS '指标的具体数值，使用DECIMAL类型保证精度';
COMMENT ON COLUMN monitor.metrics_history.tags IS '指标标签，JSON格式存储维度信息，如消费者组、主题、分区等';
COMMENT ON COLUMN monitor.metrics_history.collected_at IS '指标数据的实际采集时间戳';
COMMENT ON COLUMN monitor.metrics_history.created_at IS '数据库记录的创建时间';

-- 创建指标历史表索引
CREATE INDEX IF NOT EXISTS idx_metrics_service_metric_time
ON monitor.metrics_history(service_name, metric_name, collected_at);

CREATE INDEX IF NOT EXISTS idx_metrics_collected_at
ON monitor.metrics_history(collected_at);

-- 为tags字段创建GIN索引，支持JSON查询
CREATE INDEX IF NOT EXISTS idx_metrics_tags
ON monitor.metrics_history USING GIN (tags);

-- 创建服务健康状态表（用于缓存当前状态）
-- 用于快速查询各服务的当前健康状态，避免频繁查询历史数据
CREATE TABLE IF NOT EXISTS monitor.service_health (
    service_name VARCHAR(100) NOT NULL,                                      -- 服务名称，复合主键的一部分
    instance_id VARCHAR(100) NOT NULL DEFAULT 'default',                     -- 实例ID，复合主键的一部分，默认为'default'以保持向后兼容
    status VARCHAR(20) NOT NULL CHECK (status IN ('healthy', 'unhealthy', 'unknown')), -- 当前健康状态：healthy(健康)、unhealthy(不健康)、unknown(未知)
    last_check TIMESTAMPTZ DEFAULT NOW(),                                    -- 最后一次健康检查时间
    error_message TEXT,                                                      -- 最近一次检查失败的错误信息
    consecutive_failures INTEGER DEFAULT 0,                                  -- 连续失败次数，用于判断是否需要告警
    consecutive_successes INTEGER DEFAULT 0,                                 -- 连续成功次数，用于判断是否可以自动恢复告警
    created_at TIMESTAMPTZ DEFAULT NOW(),                                    -- 记录创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW(),                                   -- 记录最后更新时间
    PRIMARY KEY (service_name, instance_id)                                  -- 复合主键：服务名称 + 实例ID
);

-- 为service_health表添加注释
COMMENT ON TABLE monitor.service_health IS '服务健康状态表，缓存各服务实例的当前健康状态信息，支持多实例监控';
COMMENT ON COLUMN monitor.service_health.service_name IS '服务名称，复合主键的一部分，标识服务';
COMMENT ON COLUMN monitor.service_health.instance_id IS '实例ID，复合主键的一部分，标识服务的具体实例，默认为default保持向后兼容';
COMMENT ON COLUMN monitor.service_health.status IS '服务实例当前健康状态：healthy(正常)、unhealthy(异常)、unknown(状态未知)';
COMMENT ON COLUMN monitor.service_health.last_check IS '最后一次执行健康检查的时间戳';
COMMENT ON COLUMN monitor.service_health.error_message IS '健康检查失败时记录的错误详情';
COMMENT ON COLUMN monitor.service_health.consecutive_failures IS '连续健康检查失败的次数，达到阈值时触发告警';
COMMENT ON COLUMN monitor.service_health.consecutive_successes IS '连续健康检查成功的次数，达到阈值时自动恢复告警';
COMMENT ON COLUMN monitor.service_health.created_at IS '服务实例首次加入监控的时间';
COMMENT ON COLUMN monitor.service_health.updated_at IS '健康状态最后更新时间';

-- 创建服务健康状态表索引
-- 状态索引，用于按状态查询服务实例
CREATE INDEX IF NOT EXISTS idx_service_health_status
ON monitor.service_health(status);

-- 最后检查时间索引，用于查询需要检查的服务实例
CREATE INDEX IF NOT EXISTS idx_service_health_last_check
ON monitor.service_health(last_check);

-- 服务名称索引，用于快速查询特定服务的所有实例
CREATE INDEX IF NOT EXISTS idx_service_health_service_name
ON monitor.service_health(service_name);

-- 复合索引，用于优化按服务名称和状态查询
CREATE INDEX IF NOT EXISTS idx_service_health_service_status
ON monitor.service_health(service_name, status);

-- 插入一些初始数据用于测试
INSERT INTO monitor.service_health (service_name, instance_id, status, last_check) VALUES
('monitor-service', 'default', 'healthy', NOW())
ON CONFLICT (service_name, instance_id) DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION monitor.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_alerts_updated_at BEFORE UPDATE ON monitor.alerts
    FOR EACH ROW EXECUTE FUNCTION monitor.update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON monitor.notifications
    FOR EACH ROW EXECUTE FUNCTION monitor.update_updated_at_column();

CREATE TRIGGER update_service_health_updated_at BEFORE UPDATE ON monitor.service_health
    FOR EACH ROW EXECUTE FUNCTION monitor.update_updated_at_column();

-- 创建视图：活跃告警
-- 用于快速查询当前所有活跃的告警信息
CREATE OR REPLACE VIEW monitor.active_alerts AS
SELECT
    a.id,
    a.rule_name,
    a.service_name,
    a.status,
    a.message,
    a.severity,
    a.started_at,
    a.metadata,
    EXTRACT(EPOCH FROM (NOW() - a.started_at))::INTEGER as duration_seconds
FROM monitor.alerts a
WHERE a.status IN ('pending', 'firing')
ORDER BY a.severity DESC, a.started_at ASC;

-- 为视图添加注释
COMMENT ON VIEW monitor.active_alerts IS '活跃告警视图，显示所有当前正在进行的告警及其持续时间';

-- 创建视图：服务状态概览
-- 用于展示所有服务的健康状态和相关告警数量
CREATE OR REPLACE VIEW monitor.service_status_overview AS
SELECT
    sh.service_name,
    sh.status,
    sh.last_check,
    sh.consecutive_failures,
    COUNT(a.id) as active_alerts_count
FROM monitor.service_health sh
LEFT JOIN monitor.alerts a ON sh.service_name = a.service_name AND a.status IN ('pending', 'firing')
GROUP BY sh.service_name, sh.status, sh.last_check, sh.consecutive_failures
ORDER BY sh.service_name;

-- 为视图添加注释
COMMENT ON VIEW monitor.service_status_overview IS '服务状态概览视图，汇总各服务的健康状态和活跃告警数量';

-- 授予权限（如果需要）
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA monitor TO monitor;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA monitor TO monitor;
-- GRANT USAGE ON SCHEMA monitor TO monitor;
