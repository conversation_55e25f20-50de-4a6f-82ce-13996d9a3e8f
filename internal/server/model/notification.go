package model

import (
	"time"

	"gorm.io/gorm"
)

// Notification 通知记录模型
type Notification struct {
	ID           uint                `gorm:"primaryKey;column:id" json:"id"`                                // 通知记录唯一标识
	AlertID      uint                `gorm:"column:alert_id;not null" json:"alert_id"`                      // 关联的告警记录ID
	Channel      NotificationChannel `gorm:"column:channel;size:50;not null" json:"channel"`                // 通知渠道
	Recipient    string              `gorm:"column:recipient;size:200;not null" json:"recipient"`           // 通知接收者
	Content      string              `gorm:"column:content;type:text" json:"content"`                       // 发送的内容
	Status       NotificationStatus  `gorm:"column:status;size:20;not null" json:"status"`                  // 发送状态
	SentAt       *time.Time          `gorm:"column:sent_at" json:"sent_at"`                                 // 实际发送时间
	ErrorMessage string              `gorm:"column:error_message;type:text" json:"error_message"`           // 发送失败错误信息
	RetryCount   int                 `gorm:"column:retry_count;default:0" json:"retry_count"`               // 重试次数
	CreatedAt    time.Time           `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"` // 记录创建时间
	UpdatedAt    time.Time           `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"` // 记录最后更新时间

	// 关联关系
	Alert Alert `gorm:"foreignKey:AlertID" json:"alert,omitempty"` // 关联的告警记录
}

// NotificationChannel 通知渠道枚举
type NotificationChannel string

const (
	NotificationChannelEmail   NotificationChannel = "email"   // 邮件
	NotificationChannelSMS     NotificationChannel = "sms"     // 短信
	NotificationChannelWebhook NotificationChannel = "webhook" // Webhook
)

// NotificationStatus 通知状态枚举
type NotificationStatus string

const (
	NotificationStatusPending NotificationStatus = "pending" // 待发送
	NotificationStatusSent    NotificationStatus = "sent"    // 已发送
	NotificationStatusFailed  NotificationStatus = "failed"  // 发送失败
)

// TableName 指定表名
func (Notification) TableName() string {
	return "monitor.notifications"
}

// IsPending 判断是否待发送
func (n *Notification) IsPending() bool {
	return n.Status == NotificationStatusPending
}

// IsSent 判断是否已发送
func (n *Notification) IsSent() bool {
	return n.Status == NotificationStatusSent
}

// IsFailed 判断是否发送失败
func (n *Notification) IsFailed() bool {
	return n.Status == NotificationStatusFailed
}

// MarkAsSent 标记为已发送
func (n *Notification) MarkAsSent() {
	now := time.Now()
	n.Status = NotificationStatusSent
	n.SentAt = &now
	n.ErrorMessage = ""
}

// MarkAsFailed 标记为发送失败
func (n *Notification) MarkAsFailed(errorMsg string) {
	n.Status = NotificationStatusFailed
	n.ErrorMessage = errorMsg
	n.RetryCount++
}

// CanRetry 判断是否可以重试
func (n *Notification) CanRetry(maxRetries int) bool {
	return n.IsFailed() && n.RetryCount < maxRetries
}

// BeforeCreate GORM钩子：创建前
func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.Status == "" {
		n.Status = NotificationStatusPending
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (n *Notification) BeforeUpdate(tx *gorm.DB) error {
	n.UpdatedAt = time.Now()
	return nil
}
