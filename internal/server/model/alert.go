package model

import (
	"time"

	"gorm.io/gorm"
)

// Alert 告警记录模型
type Alert struct {
	ID          uint          `gorm:"primaryKey;column:id" json:"id"`                                // 告警记录唯一标识
	RuleName    string        `gorm:"column:rule_name;size:100;not null" json:"rule_name"`           // 触发告警的规则名称
	ServiceName string        `gorm:"column:service_name;size:100;not null" json:"service_name"`     // 产生告警的服务名称
	Status      AlertStatus   `gorm:"column:status;size:20;not null" json:"status"`                  // 告警状态
	Message     string        `gorm:"column:message;type:text;not null" json:"message"`              // 告警详细消息内容
	Severity    AlertSeverity `gorm:"column:severity;size:20;not null" json:"severity"`              // 告警严重级别
	StartedAt   time.Time     `gorm:"column:started_at;default:CURRENT_TIMESTAMP" json:"started_at"` // 告警开始时间
	ResolvedAt  *time.Time    `gorm:"column:resolved_at" json:"resolved_at"`                         // 告警解决时间
	Metadata    string        `gorm:"column:metadata;type:jsonb" json:"metadata"`                    // 告警相关的元数据
	CreatedAt   time.Time     `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"` // 记录创建时间
	UpdatedAt   time.Time     `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"` // 记录最后更新时间

	// 关联关系
	Notifications []Notification `gorm:"foreignKey:AlertID" json:"notifications,omitempty"` // 关联的通知记录
}

// AlertStatus 告警状态枚举
type AlertStatus string

const (
	AlertStatusPending  AlertStatus = "pending"  // 待处理
	AlertStatusFiring   AlertStatus = "firing"   // 告警中
	AlertStatusResolved AlertStatus = "resolved" // 已解决
)

// AlertSeverity 告警严重级别枚举
type AlertSeverity string

const (
	AlertSeverityInfo     AlertSeverity = "info"     // 信息
	AlertSeverityWarning  AlertSeverity = "warning"  // 警告
	AlertSeverityCritical AlertSeverity = "critical" // 严重
)

// TableName 指定表名
func (Alert) TableName() string {
	return "monitor.alerts"
}

// IsActive 判断告警是否活跃
func (a *Alert) IsActive() bool {
	return a.Status == AlertStatusPending || a.Status == AlertStatusFiring
}

// Duration 获取告警持续时间
func (a *Alert) Duration() time.Duration {
	if a.ResolvedAt != nil {
		return a.ResolvedAt.Sub(a.StartedAt)
	}
	return time.Since(a.StartedAt)
}

// Resolve 解决告警
func (a *Alert) Resolve() {
	now := time.Now()
	a.Status = AlertStatusResolved
	a.ResolvedAt = &now
}

// BeforeCreate GORM钩子：创建前
func (a *Alert) BeforeCreate(tx *gorm.DB) error {
	if a.StartedAt.IsZero() {
		a.StartedAt = time.Now()
	}
	// 确保 metadata 是有效的 JSON
	if a.Metadata == "" {
		a.Metadata = "{}"
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (a *Alert) BeforeUpdate(tx *gorm.DB) error {
	a.UpdatedAt = time.Now()
	return nil
}
