package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ServiceHealth 服务健康状态模型
type ServiceHealth struct {
	ServiceName          string       `gorm:"primaryKey;column:service_name;size:100;not null" json:"service_name"`                 // 服务名称，复合主键的一部分
	InstanceID           string       `gorm:"primaryKey;column:instance_id;size:100;not null;default:'default'" json:"instance_id"` // 实例ID，复合主键的一部分，默认为'default'
	Status               HealthStatus `gorm:"column:status;size:20;not null" json:"status"`                                         // 当前健康状态
	LastCheck            time.Time    `gorm:"column:last_check;default:CURRENT_TIMESTAMP" json:"last_check"`                        // 最后一次健康检查时间
	ErrorMessage         string       `gorm:"column:error_message;type:text" json:"error_message"`                                  // 最近一次检查失败的错误信息
	ConsecutiveFailures  int          `gorm:"column:consecutive_failures;default:0" json:"consecutive_failures"`                    // 连续失败次数
	ConsecutiveSuccesses int          `gorm:"column:consecutive_successes;default:0" json:"consecutive_successes"`                  // 连续成功次数
	CreatedAt            time.Time    `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`                        // 记录创建时间
	UpdatedAt            time.Time    `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`                        // 记录最后更新时间
}

// HealthStatus 健康状态枚举
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"   // 健康
	HealthStatusUnhealthy HealthStatus = "unhealthy" // 不健康
	HealthStatusUnknown   HealthStatus = "unknown"   // 未知
)

// TableName 指定表名
func (ServiceHealth) TableName() string {
	return "monitor.service_health"
}

// IsHealthy 判断是否健康
func (sh *ServiceHealth) IsHealthy() bool {
	return sh.Status == HealthStatusHealthy
}

// IsUnhealthy 判断是否不健康
func (sh *ServiceHealth) IsUnhealthy() bool {
	return sh.Status == HealthStatusUnhealthy
}

// IsUnknown 判断状态是否未知
func (sh *ServiceHealth) IsUnknown() bool {
	return sh.Status == HealthStatusUnknown
}

// UpdateHealthy 更新为健康状态
func (sh *ServiceHealth) UpdateHealthy() {
	sh.Status = HealthStatusHealthy
	sh.LastCheck = time.Now()
	sh.ErrorMessage = ""
	sh.ConsecutiveFailures = 0
	sh.ConsecutiveSuccesses++
}

// UpdateUnhealthy 更新为不健康状态
func (sh *ServiceHealth) UpdateUnhealthy(errorMsg string) {
	sh.Status = HealthStatusUnhealthy
	sh.LastCheck = time.Now()
	sh.ErrorMessage = errorMsg
	sh.ConsecutiveFailures++
	sh.ConsecutiveSuccesses = 0
}

// UpdateUnknown 更新为未知状态
func (sh *ServiceHealth) UpdateUnknown(errorMsg string) {
	sh.Status = HealthStatusUnknown
	sh.LastCheck = time.Now()
	sh.ErrorMessage = errorMsg
}

// ShouldAlert 判断是否应该告警
func (sh *ServiceHealth) ShouldAlert(threshold int) bool {
	return sh.IsUnhealthy() && sh.ConsecutiveFailures >= threshold
}

// GetLastCheckDuration 获取距离上次检查的时间
func (sh *ServiceHealth) GetLastCheckDuration() time.Duration {
	return time.Since(sh.LastCheck)
}

// IsDefaultInstance 判断是否为默认实例（单实例模式）
func (sh *ServiceHealth) IsDefaultInstance() bool {
	return sh.InstanceID == "default"
}

// GetInstanceKey 获取实例的唯一标识键
func (sh *ServiceHealth) GetInstanceKey() string {
	return fmt.Sprintf("%s:%s", sh.ServiceName, sh.InstanceID)
}

// SetInstanceID 设置实例ID，如果为空则使用默认值
func (sh *ServiceHealth) SetInstanceID(instanceID string) {
	if instanceID == "" {
		sh.InstanceID = "default"
	} else {
		sh.InstanceID = instanceID
	}
}

// BeforeCreate GORM钩子：创建前
func (sh *ServiceHealth) BeforeCreate(tx *gorm.DB) error {
	if sh.Status == "" {
		sh.Status = HealthStatusUnknown
	}
	if sh.LastCheck.IsZero() {
		sh.LastCheck = time.Now()
	}
	// 为向后兼容性设置默认实例ID
	if sh.InstanceID == "" {
		sh.InstanceID = "default"
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (sh *ServiceHealth) BeforeUpdate(tx *gorm.DB) error {
	sh.UpdatedAt = time.Now()
	return nil
}
