package model

import (
	"gorm.io/gorm"
	"time"
)

// MetricsHistory 监控指标历史模型
type MetricsHistory struct {
	ID          uint      `gorm:"primaryKey;column:id" json:"id"`                                      // 指标记录唯一标识
	ServiceName string    `gorm:"column:service_name;size:100;not null" json:"service_name"`           // 服务名称
	MetricName  string    `gorm:"column:metric_name;size:100;not null" json:"metric_name"`             // 指标名称
	MetricValue float64   `gorm:"column:metric_value;type:decimal(15,2);not null" json:"metric_value"` // 指标数值
	Tags        string    `gorm:"column:tags;type:jsonb" json:"tags"`                                  // 指标标签（JSON格式，如消费者组、主题、分区等）
	CollectedAt time.Time `gorm:"column:collected_at;default:CURRENT_TIMESTAMP" json:"collected_at"`   // 指标采集时间
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`       // 记录创建时间
}

// TableName 指定表名
func (MetricsHistory) TableName() string {
	return "monitor.metrics_history"
}

// BeforeCreate GORM钩子：创建前
func (mh *MetricsHistory) BeforeCreate(tx *gorm.DB) error {
	if mh.CollectedAt.IsZero() {
		mh.CollectedAt = time.Now()
	}
	return nil
}

// MetricType 常用指标类型常量
const (
	// EMQX相关指标
	MetricConnections = "connections"  // 连接数
	MetricMessageRate = "message_rate" // 消息速率
	MetricByteRate    = "byte_rate"    // 字节速率

	// EMQX速率监控专用指标
	MetricEMQXCurrentRate  = "emqx_current_rate" // 当前消息速率
	MetricEMQXBaseline     = "emqx_baseline"     // 基线速率
	MetricEMQXRate         = "emqx_rate_drop"    // 速率下降百分比
	MetricEMQXConnectivity = "emqx_connectivity" // EMQX连接状态

	// 系统资源指标
	MetricCPUUsage    = "cpu_usage"    // CPU使用率
	MetricMemoryUsage = "memory_usage" // 内存使用率
	MetricDiskUsage   = "disk_usage"   // 磁盘使用率
	MetricNetworkIn   = "network_in"   // 网络入流量
	MetricNetworkOut  = "network_out"  // 网络出流量

	// 应用性能指标
	MetricResponseTime = "response_time" // 响应时间
	MetricThroughput   = "throughput"    // 吞吐量
	MetricErrorRate    = "error_rate"    // 错误率

	// Kafka相关指标
	MetricTopicLag          = "topic_lag"          // 消费者组消费单个主题的延迟（主题级别）
	MetricPartitionLag      = "partition_lag"      // 单个分区延迟（分区级别）
	MetricTotalLag          = "total_lag"          // 消费者组所有主题的总延迟（统计信息）
	MetricConsumeRate       = "consume_rate"       // 消费速率
	MetricKafkaConnectivity = "kafka_connectivity" // Kafka连接状态
	MetricConsumerOffset    = "consumer_offset"    // 消费者偏移量
	MetricProducerOffset    = "producer_offset"    // 生产者偏移量
	MetricPartitionCount    = "partition_count"    // 分区数量
	MetricTopicCount        = "topic_count"        // 主题数量
)
