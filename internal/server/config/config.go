package config

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"monitor/internal/shared/logger"
	"monitor/pkg/nacos"

	"gopkg.in/yaml.v3"
)

// Config 应用程序完整配置结构（从Nacos获取）
type Config struct {
	Server          ServerConfig          `yaml:"server"`
	Database        DatabaseConfig        `yaml:"database"`
	Logging         LoggingConfig         `yaml:"logging"`
	AgentDefaults   AgentDefaultsConfig   `yaml:"agent_defaults"`
	MonitorDefaults MonitorDefaultsConfig `yaml:"monitor_defaults"`
	Services        []ServiceConfig       `yaml:"services"`
	AlertRules      []AlertRuleConfig     `yaml:"alert_rules"`
	Notifications   NotificationConfig    `yaml:"notifications"`
	AutoResolve     AutoResolveConfig     `yaml:"auto_resolve"`
}

// LocalConfig 本地配置结构（仅包含Nacos连接信息）
type LocalConfig struct {
	Nacos nacos.NacosConfig `yaml:"nacos"`
}

// ServerConfig HTTP服务器配置
type ServerConfig struct {
	Port int `yaml:"port"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Schema   string `yaml:"schema"`
	MaxConns int    `yaml:"max_connections"`
	SSLMode  string `yaml:"ssl_mode"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
}

// AgentDefaultsConfig Agent默认配置
type AgentDefaultsConfig struct {
	Port       int    `yaml:"port"`
	Token      string `yaml:"token"`
	Timeout    int    `yaml:"timeout"`
	RetryCount int    `yaml:"retry_count"`
}

// MonitorDefaultsConfig 监控默认配置
type MonitorDefaultsConfig struct {
	Type            string               `yaml:"type"`
	Method          string               `yaml:"method"`
	CheckInterval   int                  `yaml:"check_interval"`
	Timeout         int                  `yaml:"timeout"`
	RetryCount      int                  `yaml:"retry_count"`
	ExpectedStatus  []int                `yaml:"expected_status"`
	HealthPath      string               `yaml:"health_path"`
	AllowedNetworks []string             `yaml:"allowed_networks"` // 允许监控的网段列表，支持CIDR和通配符格式
	Docker          DockerDefaultsConfig `yaml:"docker"`
}

// DockerDefaultsConfig Docker默认配置
type DockerDefaultsConfig struct {
	RestartPolicy   string `yaml:"restart_policy"`
	MaxRestartCount int    `yaml:"max_restart_count"`
	RestartCooldown string `yaml:"restart_cooldown"`
}

// ServiceInstance 服务实例信息
type ServiceInstance struct {
	ID       string `json:"id"`       // 实例ID，格式为IP:Port
	IP       string `json:"ip"`       // 实例IP地址
	Port     uint64 `json:"port"`     // 实例端口
	Endpoint string `json:"endpoint"` // 完整的健康检查端点URL
	Healthy  bool   `json:"healthy"`  // 实例健康状态
}

// GenerateID 生成实例ID
func (si *ServiceInstance) GenerateID() {
	si.ID = fmt.Sprintf("%s:%d", si.IP, si.Port)
}

// GetAddress 获取实例地址
func (si *ServiceInstance) GetAddress() string {
	return fmt.Sprintf("%s:%d", si.IP, si.Port)
}

// IsHealthy 判断实例是否健康
func (si *ServiceInstance) IsHealthy() bool {
	return si.Healthy
}

// SetHealthy 设置实例健康状态
func (si *ServiceInstance) SetHealthy(healthy bool) {
	si.Healthy = healthy
}

// BuildEndpoint 构建健康检查端点
func (si *ServiceInstance) BuildEndpoint(healthPath string) {
	if healthPath == "" {
		healthPath = "/health"
	}
	si.Endpoint = fmt.Sprintf("http://%s:%d%s", si.IP, si.Port, healthPath)
}

// ServiceConfig 监控服务配置
type ServiceConfig struct {
	// 基本配置
	Name           string `yaml:"name"`                      // 服务名称（用于标识、日志、数据库，同时也是Nacos服务名）
	Type           string `yaml:"type,omitempty"`            // 可选，使用默认配置
	Endpoint       string `yaml:"endpoint,omitempty"`        // 可选，如果不配置则从服务发现获取
	Method         string `yaml:"method,omitempty"`          // 可选，使用默认配置
	CheckInterval  int    `yaml:"check_interval,omitempty"`  // 可选，使用默认配置
	Timeout        int    `yaml:"timeout,omitempty"`         // 可选，使用默认配置
	RetryCount     int    `yaml:"retry_count,omitempty"`     // 可选，使用默认配置
	ExpectedStatus []int  `yaml:"expected_status,omitempty"` // 可选，使用默认配置

	// 注意：多实例监控已默认启用，不再需要配置项

	// 内部标识字段（不在YAML中序列化）
	IsDiscovered bool              `yaml:"-"`                     // 标识是否为自动发现的服务
	HealthPath   string            `yaml:"health_path,omitempty"` // 可选，使用默认配置
	NacosIP      string            `yaml:"-"`                     // 从Nacos获取的IP，用于Agent Host设置
	InstanceID   string            `yaml:"-"`                     // 当前实例ID，运行时生成，格式为IP:Port
	Instances    []ServiceInstance `yaml:"-"`                     // 从Nacos动态获取的实例列表，运行时填充

	// 认证和阈值配置
	Auth       *AuthConfig      `yaml:"auth,omitempty"`
	Thresholds *ThresholdConfig `yaml:"thresholds,omitempty"`

	// Docker和Agent配置（默认使用Agent）
	Docker     *DockerConfig `yaml:"docker,omitempty"`
	AgentPort  int           `yaml:"agent_port,omitempty"`  // 覆盖默认Agent端口
	AgentToken string        `yaml:"agent_token,omitempty"` // 覆盖默认Agent token

	// Kafka监控配置
	Kafka *KafkaConfig `yaml:"kafka,omitempty"`

	// EMQX监控配置
	EMQX *EMQXConfig `yaml:"emqx,omitempty"`
}

// IsMultiInstanceEnabled 判断是否启用多实例监控
// 注意：现在所有HTTP服务默认启用多实例监控
func (sc *ServiceConfig) IsMultiInstanceEnabled() bool {
	// 基础设施服务使用单实例模式
	if sc.Type == "kafka" || sc.Type == "emqx" {
		return false
	}
	// 对于business_api类型且已手动配置endpoint的服务，使用单实例模式
	if sc.Type == "business_api" && sc.Endpoint != "" {
		return false
	}
	// 其他所有HTTP服务默认使用多实例监控
	return true
}

// IsSingleInstance 判断是否为单实例模式
func (sc *ServiceConfig) IsSingleInstance() bool {
	return !sc.IsMultiInstanceEnabled()
}

// GetInstanceCount 获取实例数量
func (sc *ServiceConfig) GetInstanceCount() int {
	if sc.IsSingleInstance() {
		return 1 // 单实例模式
	}
	return len(sc.Instances)
}

// HasInstances 判断是否有实例信息
func (sc *ServiceConfig) HasInstances() bool {
	return len(sc.Instances) > 0
}

// GetInstanceByID 根据实例ID获取实例信息
func (sc *ServiceConfig) GetInstanceByID(instanceID string) *ServiceInstance {
	for i := range sc.Instances {
		if sc.Instances[i].ID == instanceID {
			return &sc.Instances[i]
		}
	}
	return nil
}

// AddInstance 添加实例信息
func (sc *ServiceConfig) AddInstance(instance ServiceInstance) {
	// 检查是否已存在相同ID的实例
	for i := range sc.Instances {
		if sc.Instances[i].ID == instance.ID {
			// 更新现有实例
			sc.Instances[i] = instance
			return
		}
	}
	// 添加新实例
	sc.Instances = append(sc.Instances, instance)
}

// RemoveInstance 移除实例
func (sc *ServiceConfig) RemoveInstance(instanceID string) bool {
	for i := range sc.Instances {
		if sc.Instances[i].ID == instanceID {
			// 移除实例
			sc.Instances = append(sc.Instances[:i], sc.Instances[i+1:]...)
			return true
		}
	}
	return false
}

// GetHealthyInstances 获取健康的实例列表
func (sc *ServiceConfig) GetHealthyInstances() []ServiceInstance {
	var healthy []ServiceInstance
	for _, instance := range sc.Instances {
		if instance.Healthy {
			healthy = append(healthy, instance)
		}
	}
	return healthy
}

// GetUnhealthyInstances 获取不健康的实例列表
func (sc *ServiceConfig) GetUnhealthyInstances() []ServiceInstance {
	var unhealthy []ServiceInstance
	for _, instance := range sc.Instances {
		if !instance.Healthy {
			unhealthy = append(unhealthy, instance)
		}
	}
	return unhealthy
}

// ClearInstances 清空实例列表
func (sc *ServiceConfig) ClearInstances() {
	sc.Instances = sc.Instances[:0]
}

// AuthConfig 认证配置
type AuthConfig struct {
	Username string `yaml:"username"` // MQTT用户名
	Password string `yaml:"password"` // MQTT密码
}

// DockerConfig Docker容器配置
type DockerConfig struct {
	ContainerName   string       `yaml:"container_name"`
	RestartPolicy   string       `yaml:"restart_policy"`
	MaxRestartCount int          `yaml:"max_restart_count"`
	RestartCooldown string       `yaml:"restart_cooldown"`
	Agent           *AgentConfig `yaml:"agent,omitempty"`
}

// AgentConfig Agent代理配置
type AgentConfig struct {
	Host       string `yaml:"host"`
	Port       int    `yaml:"port"`
	Token      string `yaml:"token"`
	Timeout    int    `yaml:"timeout"`
	RetryCount int    `yaml:"retry_count"`
}

// ThresholdConfig 阈值配置
type ThresholdConfig struct {
	ConnectionSpike int `yaml:"connection_spike"`
	MessageRateHigh int `yaml:"message_rate_high"`
	ByteRateHigh    int `yaml:"byte_rate_high"`
	CPUUsage        int `yaml:"cpu_usage"`
	MemoryUsage     int `yaml:"memory_usage"`
	DiskUsage       int `yaml:"disk_usage"`
	ResponseTime    int `yaml:"response_time"`

	// Kafka相关阈值
	TopicLag     int64 `yaml:"topic_lag"`     // 消费者组消费单个主题的延迟阈值（主题级别）
	PartitionLag int64 `yaml:"partition_lag"` // 单个分区延迟阈值（分区级别）
	ConsumeRate  int   `yaml:"consume_rate"`  // 消费速率阈值

	// EMQX相关阈值
	EMQXRateThreshold int `yaml:"emqx_rate_drop_threshold"` // EMQX速率下降阈值（百分比）
}

// KafkaConfig Kafka监控配置
type KafkaConfig struct {
	Brokers        []string         `yaml:"brokers"`          // Kafka集群地址列表
	ConsumerGroups []string         `yaml:"consumer_groups"`  // 要监控的消费者组列表
	Topics         []string         `yaml:"topics,omitempty"` // 要监控的主题列表（可选，为空时监控所有消费者组订阅的主题）
	Version        string           `yaml:"version"`          // Kafka版本
	Auth           *KafkaSASLConfig `yaml:"auth,omitempty"`   // SASL认证配置
	TLS            *KafkaTLSConfig  `yaml:"tls,omitempty"`    // TLS加密配置
	Timeout        int              `yaml:"timeout"`          // 连接超时时间（秒）
	RetryCount     int              `yaml:"retry_count"`      // 重试次数
}

// KafkaSASLConfig Kafka SASL认证配置
type KafkaSASLConfig struct {
	Mechanism string `yaml:"mechanism"` // 认证机制：PLAIN, SCRAM-SHA-256, SCRAM-SHA-512
	Username  string `yaml:"username"`  // 用户名
	Password  string `yaml:"password"`  // 密码
}

// KafkaTLSConfig Kafka TLS加密配置
type KafkaTLSConfig struct {
	Enabled            bool   `yaml:"enabled"`              // 是否启用TLS
	InsecureSkipVerify bool   `yaml:"insecure_skip_verify"` // 是否跳过证书验证
	CertFile           string `yaml:"cert_file"`            // 客户端证书文件路径
	KeyFile            string `yaml:"key_file"`             // 客户端私钥文件路径
	CAFile             string `yaml:"ca_file"`              // CA证书文件路径
}

// EMQXConfig EMQX监控配置
type EMQXConfig struct {
	Host            string      `yaml:"host"`                  // EMQX服务器地址
	Port            int         `yaml:"port"`                  // EMQX MQTT端口
	Topics          []string    `yaml:"topics"`                // 要监控的主题列表
	RateWindow      int         `yaml:"rate_window"`           // 当前速率计算窗口（秒）
	BaselineWindow  int         `yaml:"baseline_window"`       // 基线计算窗口（秒）
	MinBaselineRate int         `yaml:"min_baseline_rate"`     // 最小基线速率保护
	BucketInterval  int         `yaml:"bucket_interval"`       // 时间桶间隔（秒）
	Auth            *AuthConfig `yaml:"auth,omitempty"`        // MQTT认证配置
	Timeout         int         `yaml:"timeout,omitempty"`     // MQTT连接超时时间（秒）
	RetryCount      int         `yaml:"retry_count,omitempty"` // 重试次数
}

// AlertRuleConfig 告警规则配置
type AlertRuleConfig struct {
	Name                 string               `yaml:"name"`
	Services             []string             `yaml:"services"`
	Condition            AlertConditionConfig `yaml:"condition"`
	Severity             string               `yaml:"severity"`
	Enabled              bool                 `yaml:"enabled"`
	AutoRestartContainer bool                 `yaml:"auto_restart_container,omitempty"` // 告警触发时是否自动重启容器
	RestartContainerName string               `yaml:"restart_container_name,omitempty"` // 指定要重启的容器名称，为空时使用服务配置中的默认容器
}

// AlertConditionConfig 告警条件配置
type AlertConditionConfig struct {
	Type                string                 `yaml:"type"`
	Threshold           interface{}            `yaml:"threshold"`
	Window              string                 `yaml:"window"`
	DeviationMultiplier float64                `yaml:"deviation_multiplier,omitempty"`
	Thresholds          map[string]interface{} `yaml:"thresholds,omitempty"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Channels    NotificationChannels `yaml:"channels"`
	Groups      []NotificationGroup  `yaml:"groups"`
	Suppression SuppressionConfig    `yaml:"suppression"`
}

// NotificationChannels 通知渠道配置
type NotificationChannels struct {
	Email   *EmailConfig   `yaml:"email,omitempty"`
	SMS     *SMSConfig     `yaml:"sms,omitempty"`
	Webhook *WebhookConfig `yaml:"webhook,omitempty"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	// SMTP配置（传统邮件发送方式）
	SMTPHost   string `yaml:"smtp_host"`
	SMTPPort   int    `yaml:"smtp_port"`
	Username   string `yaml:"username"`
	Password   string `yaml:"password"`
	TLSEnabled bool   `yaml:"tls_enabled"`

	// HTTP接口配置（新增：支持HTTP邮件接口）
	URL            string `yaml:"url"`             // 邮件接口请求地址
	TimeoutSeconds int    `yaml:"timeout_seconds"` // 请求超时时间（秒）
}

// SMSConfig 短信配置
type SMSConfig struct {
	URL            string `yaml:"url"`             // 短信接口请求地址
	Username       string `yaml:"username"`        // 短信平台账号
	Password       string `yaml:"password"`        // 短信平台密码
	TimeoutSeconds int    `yaml:"timeout_seconds"` // 请求超时时间（秒）
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
	URL        string `yaml:"url"`
	Timeout    int    `yaml:"timeout"`
	RetryCount int    `yaml:"retry_count"`
}

// NotificationGroup 通知分组
type NotificationGroup struct {
	Name       string              `yaml:"name"`
	Services   []string            `yaml:"services"`
	Channels   []string            `yaml:"channels"`
	Recipients map[string][]string `yaml:"recipients"`
	Escalation []EscalationLevel   `yaml:"escalation"`
}

// EscalationLevel 升级策略
type EscalationLevel struct {
	Level      int                 `yaml:"level"`
	Delay      string              `yaml:"delay"`
	Channels   []string            `yaml:"channels"`
	Recipients map[string][]string `yaml:"recipients,omitempty"`
}

// SuppressionConfig 告警抑制配置
type SuppressionConfig struct {
	Rules []SuppressionRule `yaml:"rules"`
}

// SuppressionRule 抑制规则
type SuppressionRule struct {
	Name      string   `yaml:"name"`
	Condition string   `yaml:"condition"`
	Window    string   `yaml:"window,omitempty"`
	MaxAlerts int      `yaml:"max_alerts,omitempty"`
	StartTime string   `yaml:"start_time,omitempty"`
	EndTime   string   `yaml:"end_time,omitempty"`
	Timezone  string   `yaml:"timezone,omitempty"`
	Services  []string `yaml:"services,omitempty"`
}

// AutoResolveConfig 告警自动恢复配置
type AutoResolveConfig struct {
	Enabled                bool `yaml:"enabled"`                  // 是否启用自动恢复
	HealthyChecksRequired  int  `yaml:"healthy_checks_required"`  // HTTP服务需要连续多少次健康检查通过才自动恢复告警
	KafkaRecoveryThreshold int  `yaml:"kafka_recovery_threshold"` // Kafka延迟恢复阈值百分比（0-100），延迟低于阈值的这个百分比时自动恢复
	EMQXRecoveryThreshold  int  `yaml:"emqx_recovery_threshold"`  // EMQX速率恢复阈值百分比（0-100），速率恢复到基线的这个百分比时自动恢复告警
	SendNotification       bool `yaml:"send_notification"`        // 是否发送恢复通知
}

// LoadLocal 加载本地Nacos连接配置
func LoadLocal() (*LocalConfig, error) {
	nacosConfigPath := filepath.Join("configs", "server", "nacos.yml")

	data, err := os.ReadFile(nacosConfigPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read nacos config file: %w", err)
	}

	var localCfg LocalConfig
	if err := yaml.Unmarshal(data, &localCfg); err != nil {
		return nil, fmt.Errorf("failed to parse nacos config: %w", err)
	}

	return &localCfg, nil
}

// LoadFromFile 从文件直接加载配置（用于测试）
func LoadFromFile(filePath string) (*Config, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析配置内容
	var fileConfig struct {
		Monitor Config `yaml:"monitor"`
	}

	if err := yaml.Unmarshal(data, &fileConfig); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	cfg := &fileConfig.Monitor

	// 设置默认值
	setDefaultValues(cfg)

	// 从环境变量覆盖配置
	loadFromEnv(cfg)

	return cfg, nil
}

// LoadFromNacos 从Nacos配置中心加载完整配置
func LoadFromNacos(nacosClient NacosClient) (*Config, error) {
	// 从Nacos获取配置内容
	configContent, err := nacosClient.GetConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get config from nacos: %w", err)
	}

	// 解析配置内容
	var nacosConfig struct {
		Monitor Config `yaml:"monitor"`
	}

	if err := yaml.Unmarshal([]byte(configContent), &nacosConfig); err != nil {
		return nil, fmt.Errorf("failed to parse nacos config content: %w", err)
	}

	cfg := &nacosConfig.Monitor

	// 设置默认值
	setDefaultValues(cfg)

	// 从环境变量覆盖配置
	loadFromEnv(cfg)

	// 验证服务配置
	for _, service := range cfg.Services {
		if err := validateServiceConfig(&service); err != nil {
			return nil, fmt.Errorf("invalid service config for service %s: %w", service.Name, err)
		}

		// 验证Kafka配置
		if service.Kafka != nil {
			if err := validateKafkaConfig(service.Kafka); err != nil {
				return nil, fmt.Errorf("invalid kafka config for service %s: %w", service.Name, err)
			}
		}
	}

	// 验证SMS配置
	if cfg.Notifications.Channels.SMS != nil {
		if err := validateSMSConfig(cfg.Notifications.Channels.SMS); err != nil {
			return nil, fmt.Errorf("invalid SMS config: %w", err)
		}
	}

	// 验证Email配置
	if cfg.Notifications.Channels.Email != nil {
		if err := validateEmailConfig(cfg.Notifications.Channels.Email); err != nil {
			return nil, fmt.Errorf("invalid Email config: %w", err)
		}
	}

	return cfg, nil
}

// LoadAndResolveFromNacos 从Nacos加载配置并解析服务
func LoadAndResolveFromNacos(nacosClient NacosClient) (*Config, error) {
	// 先加载基础配置
	cfg, err := LoadFromNacos(nacosClient)
	if err != nil {
		return nil, err
	}

	// 自动进行服务发现和解析
	resolver := NewServiceResolver(nacosClient, cfg)
	resolvedServices, err := resolver.ResolveServices()
	if err != nil {
		return nil, fmt.Errorf("failed to resolve services: %w", err)
	}
	cfg.Services = resolvedServices

	return cfg, nil
}

// hasServiceNeedingResolution 检查是否有服务需要解析
func hasServiceNeedingResolution(services []ServiceConfig) bool {
	for _, service := range services {
		// 基础设施服务类型不需要从Nacos解析endpoint
		// 包括：kafka（消息队列）、emqx（MQTT代理）等
		if service.Type == "kafka" || service.Type == "emqx" {
			continue
		}
		// 如果有服务名但没有endpoint，需要从Nacos解析
		if service.Name != "" && service.Endpoint == "" {
			return true
		}
	}
	return false
}

// NacosClient Nacos客户端接口
type NacosClient interface {
	GetConfig() (string, error)
	ListenConfig(callback func(namespace, group, dataId, data string)) error
	GetServiceInstance(serviceName string) (*nacos.ServiceInstance, error)
	GetAllServiceInstances(serviceName string) ([]*nacos.ServiceInstance, error)
	ListServices() ([]string, error)
}

// setDefaultValues 设置默认配置值
func setDefaultValues(cfg *Config) {
	if cfg.Server.Port == 0 {
		cfg.Server.Port = 8080
	}

	if cfg.Database.Host == "" {
		cfg.Database.Host = "localhost"
	}
	if cfg.Database.Port == 0 {
		cfg.Database.Port = 5432
	}
	if cfg.Database.Database == "" {
		cfg.Database.Database = "monitor"
	}
	if cfg.Database.Username == "" {
		cfg.Database.Username = "monitor"
	}
	if cfg.Database.Password == "" {
		cfg.Database.Password = "monitor123"
	}
	if cfg.Database.Schema == "" {
		cfg.Database.Schema = "monitor"
	}
	if cfg.Database.MaxConns == 0 {
		cfg.Database.MaxConns = 20
	}
	if cfg.Database.SSLMode == "" {
		cfg.Database.SSLMode = "disable"
	}

	if cfg.Logging.Level == "" {
		cfg.Logging.Level = "info"
	}
	if cfg.Logging.Format == "" {
		cfg.Logging.Format = "json"
	}

	// 设置Agent默认配置
	if cfg.AgentDefaults.Port == 0 {
		cfg.AgentDefaults.Port = 9090
	}
	if cfg.AgentDefaults.Token == "" {
		cfg.AgentDefaults.Token = "secure-agent-token"
	}
	if cfg.AgentDefaults.Timeout == 0 {
		cfg.AgentDefaults.Timeout = 30
	}
	if cfg.AgentDefaults.RetryCount == 0 {
		cfg.AgentDefaults.RetryCount = 3
	}

	// 设置监控默认配置
	if cfg.MonitorDefaults.Type == "" {
		cfg.MonitorDefaults.Type = "http"
	}
	if cfg.MonitorDefaults.Method == "" {
		cfg.MonitorDefaults.Method = "GET"
	}
	if cfg.MonitorDefaults.CheckInterval == 0 {
		cfg.MonitorDefaults.CheckInterval = 30
	}
	if cfg.MonitorDefaults.Timeout == 0 {
		cfg.MonitorDefaults.Timeout = 10
	}
	if cfg.MonitorDefaults.RetryCount == 0 {
		cfg.MonitorDefaults.RetryCount = 3
	}
	if len(cfg.MonitorDefaults.ExpectedStatus) == 0 {
		cfg.MonitorDefaults.ExpectedStatus = []int{200}
	}
	if cfg.MonitorDefaults.HealthPath == "" {
		cfg.MonitorDefaults.HealthPath = "/actuator/health"
	}
	if cfg.MonitorDefaults.Docker.RestartPolicy == "" {
		cfg.MonitorDefaults.Docker.RestartPolicy = "unless-stopped"
	}
	if cfg.MonitorDefaults.Docker.MaxRestartCount == 0 {
		cfg.MonitorDefaults.Docker.MaxRestartCount = 5
	}
	if cfg.MonitorDefaults.Docker.RestartCooldown == "" {
		cfg.MonitorDefaults.Docker.RestartCooldown = "5m"
	}

	// 设置通知配置默认值
	if cfg.Notifications.Channels.SMS != nil {
		sms := cfg.Notifications.Channels.SMS
		if sms.TimeoutSeconds == 0 {
			sms.TimeoutSeconds = 30
		}
	}

	// 设置服务级别的默认配置
	for i := range cfg.Services {
		service := &cfg.Services[i]

		// 设置Docker Agent默认配置
		if service.Docker != nil && service.Docker.Agent != nil {
			agent := service.Docker.Agent
			if agent.Port == 0 {
				agent.Port = cfg.AgentDefaults.Port
			}
			if agent.Token == "" {
				agent.Token = cfg.AgentDefaults.Token
			}
			if agent.Timeout == 0 {
				agent.Timeout = cfg.AgentDefaults.Timeout
			}
			if agent.RetryCount == 0 {
				agent.RetryCount = cfg.AgentDefaults.RetryCount
			}
		}

		// 设置Kafka默认值
		if service.Kafka != nil {
			if service.Kafka.Version == "" {
				service.Kafka.Version = "2.8.0" // 默认Kafka版本
			}
			if service.Kafka.Timeout == 0 {
				service.Kafka.Timeout = 30 // 默认30秒超时
			}
			if service.Kafka.RetryCount == 0 {
				service.Kafka.RetryCount = 3 // 默认重试3次
			}
		}

		// 设置EMQX默认配置
		if service.EMQX != nil {
			if service.EMQX.Port == 0 {
				service.EMQX.Port = 1883 // 默认EMQX MQTT端口
			}
			if service.EMQX.RateWindow == 0 {
				service.EMQX.RateWindow = 300 // 默认5分钟速率计算窗口
			}
			if service.EMQX.BaselineWindow == 0 {
				service.EMQX.BaselineWindow = 3600 // 默认1小时基线计算窗口
			}
			if service.EMQX.MinBaselineRate == 0 {
				service.EMQX.MinBaselineRate = 10 // 默认最小基线速率保护
			}
			if service.EMQX.Timeout == 0 {
				service.EMQX.Timeout = 30 // 默认30秒超时
			}
			if service.EMQX.RetryCount == 0 {
				service.EMQX.RetryCount = 3 // 默认重试3次
			}
		}
	}

	// 设置自动恢复配置默认值
	if !cfg.AutoResolve.Enabled {
		// 如果没有显式配置，默认启用自动恢复
		cfg.AutoResolve.Enabled = false
	}
	if cfg.AutoResolve.HealthyChecksRequired == 0 {
		cfg.AutoResolve.HealthyChecksRequired = 2 // 默认需要连续2次健康检查通过
	}
	if cfg.AutoResolve.KafkaRecoveryThreshold == 0 {
		cfg.AutoResolve.KafkaRecoveryThreshold = 80 // 默认延迟低于阈值的80%时自动恢复
	}
	if cfg.AutoResolve.EMQXRecoveryThreshold == 0 {
		cfg.AutoResolve.EMQXRecoveryThreshold = 80 // 默认速率恢复到基线的80%时自动恢复告警
	}
	// SendNotification 默认为false，不发送通知

	// 设置告警规则默认值
	// AutoRestartContainer 默认为false，确保向后兼容性
	// 由于使用了omitempty标签，false值不会出现在YAML中
	// 这里不需要显式设置，Go的零值已经是false
}

// StartConfigWatch 启动配置监听
func StartConfigWatch(nacosClient NacosClient, cfg *Config) error {
	return nacosClient.ListenConfig(func(namespace, group, dataId, data string) {
		// 配置变更回调
		logger.Info("配置已变更，正在重新加载...",
			"namespace", namespace,
			"group", group,
			"dataId", dataId)

		// 重新解析配置
		var nacosConfig struct {
			Monitor Config `yaml:"monitor"`
		}

		if err := yaml.Unmarshal([]byte(data), &nacosConfig); err != nil {
			logger.Error("解析更新的配置失败", "error", err)
			return
		}

		// 更新配置（这里可以根据需要实现更复杂的热更新逻辑）
		newCfg := &nacosConfig.Monitor
		setDefaultValues(newCfg)
		loadFromEnv(newCfg)

		// 这里可以添加配置变更通知机制
		logger.Info("配置重新加载成功")
	})
}

// loadFromEnv 从环境变量加载配置
func loadFromEnv(cfg *Config) {
	if port := os.Getenv("SERVER_PORT"); port != "" {
		// 这里可以添加端口解析逻辑
		if p, err := strconv.Atoi(port); err == nil {
			cfg.Server.Port = p
		}
	}

	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		cfg.Database.Host = dbHost
	}

	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		cfg.Database.Password = dbPassword
	}

	if dbSchema := os.Getenv("DB_SCHEMA"); dbSchema != "" {
		cfg.Database.Schema = dbSchema
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.Logging.Level = logLevel
	}
}

// validateKafkaConfig 验证Kafka配置
func validateKafkaConfig(kafkaConfig *KafkaConfig) error {
	if kafkaConfig == nil {
		return nil
	}

	// 验证Brokers配置
	if len(kafkaConfig.Brokers) == 0 {
		return fmt.Errorf("kafka brokers不能为空")
	}

	for _, broker := range kafkaConfig.Brokers {
		if broker == "" {
			return fmt.Errorf("kafka broker地址不能为空")
		}
		// 简单的地址格式验证
		if !strings.Contains(broker, ":") {
			return fmt.Errorf("无效的kafka broker地址格式: %s (期望格式: host:port)", broker)
		}
	}

	// 验证消费者组配置
	if len(kafkaConfig.ConsumerGroups) == 0 {
		return fmt.Errorf("kafka消费者组不能为空")
	}

	for _, group := range kafkaConfig.ConsumerGroups {
		if group == "" {
			return fmt.Errorf("kafka消费者组名称不能为空")
		}
	}

	// 验证版本格式
	if kafkaConfig.Version != "" {
		// 简单的版本格式验证
		if !strings.Contains(kafkaConfig.Version, ".") {
			return fmt.Errorf("无效的kafka版本格式: %s", kafkaConfig.Version)
		}
	}

	// 验证SASL认证配置
	if kafkaConfig.Auth != nil {
		if kafkaConfig.Auth.Mechanism == "" {
			return fmt.Errorf("kafka SASL认证机制不能为空")
		}
		validMechanisms := []string{"PLAIN", "SCRAM-SHA-256", "SCRAM-SHA-512"}
		isValid := false
		for _, mechanism := range validMechanisms {
			if kafkaConfig.Auth.Mechanism == mechanism {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的kafka SASL认证机制: %s (支持的机制: %v)", kafkaConfig.Auth.Mechanism, validMechanisms)
		}

		if kafkaConfig.Auth.Username == "" {
			return fmt.Errorf("kafka SASL用户名不能为空")
		}
		if kafkaConfig.Auth.Password == "" {
			return fmt.Errorf("kafka SASL密码不能为空")
		}
	}

	// 验证TLS配置
	if kafkaConfig.TLS != nil && kafkaConfig.TLS.Enabled {
		if kafkaConfig.TLS.CertFile != "" && kafkaConfig.TLS.KeyFile == "" {
			return fmt.Errorf("指定证书文件时需要kafka TLS密钥文件")
		}
		if kafkaConfig.TLS.KeyFile != "" && kafkaConfig.TLS.CertFile == "" {
			return fmt.Errorf("指定密钥文件时需要kafka TLS证书文件")
		}
	}

	return nil
}

// validateSMSConfig 验证短信配置
func validateSMSConfig(smsConfig *SMSConfig) error {
	if smsConfig == nil {
		return nil
	}

	// 验证基础配置
	if smsConfig.URL == "" {
		return fmt.Errorf("短信URL不能为空")
	}
	if smsConfig.Username == "" {
		return fmt.Errorf("短信用户名不能为空")
	}
	if smsConfig.Password == "" {
		return fmt.Errorf("短信密码不能为空")
	}

	return nil
}

// validateEmailConfig 验证邮件配置
func validateEmailConfig(emailConfig *EmailConfig) error {
	if emailConfig == nil {
		return nil
	}

	// 如果配置了HTTP接口，验证HTTP接口配置
	if emailConfig.URL != "" {
		// HTTP接口模式：只需要URL
		return nil
	}

	// 如果配置了SMTP，验证SMTP配置
	if emailConfig.SMTPHost != "" {
		if emailConfig.SMTPPort <= 0 {
			return fmt.Errorf("SMTP端口必须大于0")
		}
		if emailConfig.Username == "" {
			return fmt.Errorf("SMTP用户名不能为空")
		}
		if emailConfig.Password == "" {
			return fmt.Errorf("SMTP密码不能为空")
		}
		return nil
	}

	// 既没有配置HTTP接口也没有配置SMTP
	return fmt.Errorf("邮件配置必须包含HTTP接口URL或SMTP配置")
}

// validateMultiInstanceConfig 验证多实例配置
func validateMultiInstanceConfig(cfg *Config) error {
	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证服务级别的多实例配置
	for _, service := range cfg.Services {
		if service.IsMultiInstanceEnabled() {
			// 多实例模式下不应手动配置endpoint（除非是business_api类型）
			if service.Endpoint != "" && service.Type != "business_api" {
				return fmt.Errorf("服务 %s 在多实例模式下不应手动配置endpoint，应从服务发现获取", service.Name)
			}

			// 基础设施服务类型不支持多实例监控
			if service.Type == "kafka" || service.Type == "emqx" {
				return fmt.Errorf("服务 %s 的类型 %s 不支持多实例监控", service.Name, service.Type)
			}
		}
	}

	return nil
}

// validateServiceConfig 验证服务配置
func validateServiceConfig(serviceConfig *ServiceConfig) error {
	if serviceConfig == nil {
		return fmt.Errorf("服务配置不能为空")
	}

	// 验证基础配置
	if serviceConfig.Name == "" {
		return fmt.Errorf("服务名称不能为空")
	}

	// 验证实例配置（如果有）
	if len(serviceConfig.Instances) > 0 {
		for i, instance := range serviceConfig.Instances {
			if err := validateServiceInstance(&instance); err != nil {
				return fmt.Errorf("实例 %d 配置无效: %w", i, err)
			}
		}
	}

	// 验证检查间隔
	if serviceConfig.CheckInterval < 0 {
		return fmt.Errorf("检查间隔不能为负数")
	}

	// 验证超时时间
	if serviceConfig.Timeout < 0 {
		return fmt.Errorf("超时时间不能为负数")
	}

	// 验证重试次数
	if serviceConfig.RetryCount < 0 {
		return fmt.Errorf("重试次数不能为负数")
	}

	// 验证期望状态码
	for _, status := range serviceConfig.ExpectedStatus {
		if status < 100 || status > 599 {
			return fmt.Errorf("期望状态码 %d 无效，应在100-599范围内", status)
		}
	}

	// 验证Agent端口
	if serviceConfig.AgentPort < 0 || serviceConfig.AgentPort > 65535 {
		return fmt.Errorf("Agent端口 %d 无效，应在0-65535范围内", serviceConfig.AgentPort)
	}

	return nil
}

// validateServiceInstance 验证服务实例配置
func validateServiceInstance(instance *ServiceInstance) error {
	if instance == nil {
		return fmt.Errorf("实例配置不能为空")
	}

	if instance.ID == "" {
		return fmt.Errorf("实例ID不能为空")
	}

	if instance.IP == "" {
		return fmt.Errorf("实例IP不能为空")
	}

	if instance.Port == 0 {
		return fmt.Errorf("实例端口不能为0")
	}

	if instance.Port > 65535 {
		return fmt.Errorf("实例端口 %d 无效，应在1-65535范围内", instance.Port)
	}

	if instance.Endpoint == "" {
		return fmt.Errorf("实例端点不能为空")
	}

	return nil
}

// IsIPInAllowedNetworks 检查IP是否在允许的网段列表中
func IsIPInAllowedNetworks(ip string, allowedNetworks []string) bool {
	// 如果没有配置允许的网段，则允许所有IP
	if len(allowedNetworks) == 0 {
		return true
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		logger.Warn("无效的IP地址", "ip", ip)
		return false
	}

	for _, network := range allowedNetworks {
		if matchesNetwork(ip, parsedIP, network) {
			return true
		}
	}

	return false
}

// matchesNetwork 检查IP是否匹配指定的网段
func matchesNetwork(ip string, parsedIP net.IP, network string) bool {
	// 支持通配符格式，如 192.168.1.*
	if strings.Contains(network, "*") {
		return matchesWildcard(ip, network)
	}

	// 支持CIDR格式，如 ***********/24
	if strings.Contains(network, "/") {
		_, ipNet, err := net.ParseCIDR(network)
		if err != nil {
			logger.Warn("无效的CIDR网段", "network", network, "error", err)
			return false
		}
		return ipNet.Contains(parsedIP)
	}

	// 支持单个IP地址
	return ip == network
}

// matchesWildcard 检查IP是否匹配通配符模式
func matchesWildcard(ip, pattern string) bool {
	ipParts := strings.Split(ip, ".")
	patternParts := strings.Split(pattern, ".")

	if len(ipParts) != len(patternParts) {
		return false
	}

	for i, part := range patternParts {
		if part != "*" && part != ipParts[i] {
			return false
		}
	}

	return true
}
