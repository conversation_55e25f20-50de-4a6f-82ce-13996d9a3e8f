package config

import (
	"fmt"
	"net/url"
	"strings"

	"monitor/internal/shared/logger"
	"monitor/pkg/nacos"
)

// ServiceResolver 服务配置解析器
type ServiceResolver struct {
	nacosClient NacosClient
	config      *Config
}

// NewServiceResolver 创建服务配置解析器
func NewServiceResolver(nacosClient NacosClient, config *Config) *ServiceResolver {
	return &ServiceResolver{
		nacosClient: nacosClient,
		config:      config,
	}
}

// ResolveServices 解析服务配置，包括手动配置的服务和自动发现的服务
func (r *ServiceResolver) ResolveServices() ([]ServiceConfig, error) {
	var resolvedServices []ServiceConfig
	var failedServices []string

	// 1. 处理手动配置的服务
	for _, service := range r.config.Services {
		resolved, err := r.resolveService(service)
		if err != nil {
			logger.Error("解析手动配置服务失败", "service", service.Name, "error", err)
			failedServices = append(failedServices, fmt.Sprintf("%s: %v", service.Name, err))
			continue
		}

		// 多实例服务可能返回多个配置
		if len(resolved) > 0 {
			resolvedServices = append(resolvedServices, resolved...)
		}
	}

	// 2. 自动发现服务
	discoveredServices, err := r.discoverServices()
	if err != nil {
		logger.Error("自动发现服务失败", "error", err)
		// 自动发现失败不影响手动配置的服务，只记录警告
		logger.Warn("自动服务发现失败，将只使用手动配置的服务", "error", err)
	} else {
		resolvedServices = append(resolvedServices, discoveredServices...)
	}

	// 如果有手动配置的服务解析失败，立即返回错误
	if len(failedServices) > 0 {
		return nil, fmt.Errorf("手动配置服务解析失败，%d个服务无法解析:\n  - %s",
			len(failedServices),
			strings.Join(failedServices, "\n  - "))
	}

	logger.Info("服务解析完成",
		"configured_services", len(r.config.Services),
		"discovered_services", len(discoveredServices),
		"total_resolved_services", len(resolvedServices))

	return resolvedServices, nil
}

// resolveService 解析单个服务配置，支持多实例
func (r *ServiceResolver) resolveService(service ServiceConfig) ([]ServiceConfig, error) {
	// 对于非微服务类型的服务，不需要从Nacos解析endpoint
	// 这些服务类型包括：kafka（消息队列）、emqx（MQTT代理）等基础设施服务
	if service.Type == "kafka" || service.Type == "emqx" {
		logger.Debug("跳过基础设施服务的Nacos解析",
			"service", service.Name,
			"type", service.Type)
		resolved := service
		r.applyDefaults(&resolved)
		return []ServiceConfig{resolved}, nil
	}

	// 对于HTTP服务，默认启用多实例监控
	// 只有基础设施服务（kafka、emqx等）使用单实例模式
	useMultiInstance := r.shouldUseMultiInstance(service)
	logger.Debug("服务解析模式判断",
		"service", service.Name,
		"type", service.Type,
		"endpoint", service.Endpoint,
		"use_multi_instance", useMultiInstance)

	if useMultiInstance {
		return r.resolveMultiInstanceService(service)
	} else {
		return r.resolveSingleInstanceService(service)
	}
}

// shouldUseMultiInstance 判断服务是否应该使用多实例监控
func (r *ServiceResolver) shouldUseMultiInstance(service ServiceConfig) bool {
	// 基础设施服务使用单实例模式
	if service.Type == "kafka" || service.Type == "emqx" {
		return false
	}

	// 对于business_api类型且已手动配置endpoint的服务，使用单实例模式
	if service.Type == "business_api" && service.Endpoint != "" {
		return false
	}

	// 其他所有HTTP服务默认使用多实例监控
	return true
}

// resolveSingleInstanceService 解析单实例服务（原有逻辑）
func (r *ServiceResolver) resolveSingleInstanceService(service ServiceConfig) ([]ServiceConfig, error) {
	resolved := service

	// 如果没有endpoint但有服务名，从Nacos服务发现获取
	if resolved.Name != "" && resolved.Endpoint == "" {
		instance, err := r.nacosClient.GetServiceInstance(resolved.Name)
		if err != nil {
			return nil, fmt.Errorf("failed to get service instance for %s: %w", resolved.Name, err)
		}

		// 构建endpoint
		healthPath := service.HealthPath
		if healthPath == "" {
			healthPath = r.config.MonitorDefaults.HealthPath
		}
		resolved.Endpoint = fmt.Sprintf("http://%s:%d%s", instance.IP, instance.Port, healthPath)

		// 为有Docker配置的服务设置从Nacos获取的IP作为Agent Host
		if resolved.Docker != nil {
			// 将从Nacos获取的IP保存到临时字段，供applyDefaults使用
			resolved.NacosIP = instance.IP
		}
	}

	// 应用默认配置
	r.applyDefaults(&resolved)

	return []ServiceConfig{resolved}, nil
}

// resolveMultiInstanceService 解析多实例服务
func (r *ServiceResolver) resolveMultiInstanceService(service ServiceConfig) ([]ServiceConfig, error) {
	if service.Name == "" {
		return nil, fmt.Errorf("多实例服务必须指定服务名称")
	}

	// 从Nacos获取所有服务实例
	instances, err := r.nacosClient.GetAllServiceInstances(service.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to get all service instances for %s: %w", service.Name, err)
	}

	if len(instances) == 0 {
		logger.Warn("服务没有可用实例", "service", service.Name)
		return []ServiceConfig{}, nil
	}

	// 过滤和排序实例
	filteredInstances := r.filterNacosInstances(instances, service.Name)
	r.sortNacosInstances(filteredInstances)

	logger.Info("发现多个服务实例",
		"service", service.Name,
		"total_instances", len(instances),
		"filtered_instances", len(filteredInstances))

	var resolvedServices []ServiceConfig

	// 为每个实例创建独立的ServiceConfig
	for _, instance := range filteredInstances {
		instanceConfig := service // 复制基础配置

		// 生成实例ID
		instanceConfig.InstanceID = r.generateInstanceID(instance.IP, instance.Port)

		// 构建健康检查端点
		instanceConfig.Endpoint = r.buildInstanceEndpoint(instance.IP, instance.Port, service.HealthPath)

		// 设置Nacos IP用于Agent配置
		instanceConfig.NacosIP = instance.IP

		// 确保每个实例都有独立的Docker配置
		if instanceConfig.Docker != nil {
			// 深拷贝Docker配置，避免多个实例共享同一个配置对象
			dockerConfig := *instanceConfig.Docker
			instanceConfig.Docker = &dockerConfig

			// 重置Agent配置，确保每个实例都有独立的Agent配置
			instanceConfig.Docker.Agent = nil

			logger.Debug("设置实例Nacos IP",
				"service", service.Name,
				"instance_id", instanceConfig.InstanceID,
				"nacos_ip", instance.IP)
		}

		// 添加实例信息到配置中
		serviceInstance := ServiceInstance{
			ID:       instanceConfig.InstanceID,
			IP:       instance.IP,
			Port:     instance.Port,
			Endpoint: instanceConfig.Endpoint,
			Healthy:  true, // 从Nacos获取的都是健康实例
		}
		instanceConfig.Instances = []ServiceInstance{serviceInstance}

		// 验证实例配置
		if err := r.validateInstanceConfig(&instanceConfig); err != nil {
			logger.Error("实例配置验证失败",
				"service", service.Name,
				"instance_id", instanceConfig.InstanceID,
				"error", err)
			continue
		}

		// 应用默认配置
		r.applyDefaults(&instanceConfig)

		resolvedServices = append(resolvedServices, instanceConfig)

		logger.Debug("解析实例配置",
			"service", service.Name,
			"instance_id", instanceConfig.InstanceID,
			"endpoint", instanceConfig.Endpoint)
	}

	if len(resolvedServices) == 0 {
		return nil, fmt.Errorf("服务 %s 没有有效的实例配置", service.Name)
	}

	return resolvedServices, nil
}

// discoverServices 自动发现服务，默认启用多实例监控
func (r *ServiceResolver) discoverServices() ([]ServiceConfig, error) {
	// 获取所有服务
	serviceNames, err := r.nacosClient.ListServices()
	if err != nil {
		return nil, fmt.Errorf("failed to list services: %w", err)
	}

	var discoveredServices []ServiceConfig
	existingServices := make(map[string]bool)

	// 记录已手动配置的服务
	for _, service := range r.config.Services {
		if service.Name != "" {
			existingServices[service.Name] = true
		}
	}

	// 处理发现的服务
	for _, serviceName := range serviceNames {
		// 跳过已手动配置的服务
		if existingServices[serviceName] {
			logger.Debug("跳过已手动配置的服务", "service", serviceName)
			continue
		}

		// 应用过滤规则（这里可以扩展更复杂的过滤逻辑）
		if !r.shouldMonitorService(serviceName) {
			logger.Debug("跳过不需要监控的服务", "service", serviceName)
			continue
		}

		// 默认启用多实例发现
		instances, err := r.nacosClient.GetAllServiceInstances(serviceName)
		if err != nil {
			logger.Warn("获取自动发现服务所有实例失败", "service", serviceName, "error", err)
			continue
		}

		if len(instances) == 0 {
			logger.Debug("服务没有可用实例", "service", serviceName)
			continue
		}

		// 过滤和排序实例
		filteredInstances := r.filterNacosInstances(instances, serviceName)
		r.sortNacosInstances(filteredInstances)

		logger.Info("自动发现多实例服务",
			"service", serviceName,
			"total_instances", len(instances),
			"filtered_instances", len(filteredInstances))

		// 为每个实例创建独立的ServiceConfig
		for _, instance := range filteredInstances {
			service := ServiceConfig{
				Name:         serviceName,
				InstanceID:   fmt.Sprintf("%s:%d", instance.IP, instance.Port),
				Endpoint:     fmt.Sprintf("http://%s:%d%s", instance.IP, instance.Port, r.config.MonitorDefaults.HealthPath),
				IsDiscovered: true,
				NacosIP:      instance.IP,
				Docker: &DockerConfig{
					ContainerName: serviceName,
				},
			}

			// 添加实例信息到配置中
			serviceInstance := ServiceInstance{
				ID:       service.InstanceID,
				IP:       instance.IP,
				Port:     instance.Port,
				Endpoint: service.Endpoint,
				Healthy:  true, // 从Nacos获取的都是健康实例
			}
			service.Instances = []ServiceInstance{serviceInstance}

			// 应用默认配置
			r.applyDefaults(&service)
			discoveredServices = append(discoveredServices, service)
		}
	}

	logger.Info("自动服务发现完成", "discovered_count", len(discoveredServices))
	return discoveredServices, nil
}

// shouldMonitorService 判断是否应该监控某个服务
func (r *ServiceResolver) shouldMonitorService(serviceName string) bool {
	// 这里可以实现更复杂的过滤逻辑
	// 例如：基于服务名模式、元数据等

	// 简单的过滤：跳过以"nacos"开头的服务
	if strings.HasPrefix(serviceName, "nacos") {
		return false
	}

	return true
}

// applyDefaults 应用默认配置
func (r *ServiceResolver) applyDefaults(service *ServiceConfig) {
	defaults := &r.config.MonitorDefaults

	if service.Type == "" {
		service.Type = defaults.Type
	}
	if service.Method == "" {
		service.Method = defaults.Method
	}
	if service.CheckInterval == 0 {
		service.CheckInterval = defaults.CheckInterval
	}
	if service.Timeout == 0 {
		service.Timeout = defaults.Timeout
	}
	if service.RetryCount == 0 {
		service.RetryCount = defaults.RetryCount
	}
	if len(service.ExpectedStatus) == 0 {
		service.ExpectedStatus = defaults.ExpectedStatus
	}
	if service.HealthPath == "" {
		service.HealthPath = defaults.HealthPath
	}

	// 应用Docker默认配置
	if service.Docker != nil {
		dockerDefaults := &defaults.Docker
		if service.Docker.RestartPolicy == "" {
			service.Docker.RestartPolicy = dockerDefaults.RestartPolicy
		}
		if service.Docker.MaxRestartCount == 0 {
			service.Docker.MaxRestartCount = dockerDefaults.MaxRestartCount
		}
		if service.Docker.RestartCooldown == "" {
			service.Docker.RestartCooldown = dockerDefaults.RestartCooldown
		}

		// 确保所有有Docker配置的服务都有Agent配置
		if service.Docker.Agent == nil {
			service.Docker.Agent = &AgentConfig{
				Port:       r.config.AgentDefaults.Port,
				Token:      r.config.AgentDefaults.Token,
				Timeout:    r.config.AgentDefaults.Timeout,
				RetryCount: r.config.AgentDefaults.RetryCount,
			}
		}

		// 设置Agent Host（优先级：配置值 > Nacos IP > endpoint提取的IP）
		if service.Docker.Agent.Host == "" {
			if service.NacosIP != "" {
				// 使用从Nacos获取的IP
				service.Docker.Agent.Host = service.NacosIP
				logger.Debug("使用Nacos IP作为Agent Host",
					"service", service.Name,
					"nacos_ip", service.NacosIP)
			} else if service.Endpoint != "" {
				// 从endpoint中提取主机IP作为Agent主机
				if host := r.extractHostFromEndpoint(service.Endpoint); host != "" {
					service.Docker.Agent.Host = host
					logger.Debug("从endpoint提取Agent Host",
						"service", service.Name,
						"endpoint", service.Endpoint,
						"agent_host", host)
				}
			}
		}

		// 服务级别的agent_port和agent_token可以覆盖配置块中的值
		if service.AgentPort > 0 {
			service.Docker.Agent.Port = service.AgentPort
			logger.Debug("使用服务级别Agent端口覆盖",
				"service", service.Name,
				"agent_port", service.AgentPort)
		}
		if service.AgentToken != "" {
			service.Docker.Agent.Token = service.AgentToken
			logger.Debug("使用服务级别Agent Token覆盖",
				"service", service.Name)
		}

		logger.Debug("最终Agent配置",
			"service", service.Name,
			"agent_host", service.Docker.Agent.Host,
			"agent_port", service.Docker.Agent.Port)
	}
}

// extractHostFromEndpoint 从endpoint URL中提取主机IP
func (r *ServiceResolver) extractHostFromEndpoint(endpoint string) string {
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		logger.Warn("Failed to parse endpoint URL", "endpoint", endpoint, "error", err)
		return ""
	}

	// 提取主机部分（可能包含端口）
	host := parsedURL.Hostname()
	if host == "" {
		logger.Warn("Failed to extract host from endpoint", "endpoint", endpoint)
		return ""
	}

	return host
}

// filterNacosInstances 过滤Nacos服务实例
func (r *ServiceResolver) filterNacosInstances(instances []*nacos.ServiceInstance, serviceName string) []*nacos.ServiceInstance {
	var filtered []*nacos.ServiceInstance

	for _, instance := range instances {
		// 基本健康检查（Nacos已经过滤了不健康的实例）
		if instance.IP == "" || instance.Port == 0 {
			logger.Warn("跳过无效实例",
				"service", serviceName,
				"ip", instance.IP,
				"port", instance.Port)
			continue
		}

		// 网段过滤
		if !IsIPInAllowedNetworks(instance.IP, r.config.MonitorDefaults.AllowedNetworks) {
			logger.Debug("跳过不在允许网段内的实例",
				"service", serviceName,
				"ip", instance.IP,
				"allowed_networks", r.config.MonitorDefaults.AllowedNetworks)
			continue
		}

		// 可以添加更多过滤逻辑，例如：
		// - 基于元数据过滤
		// - 基于端口范围过滤

		filtered = append(filtered, instance)
	}

	logger.Debug("实例过滤完成",
		"service", serviceName,
		"total_instances", len(instances),
		"filtered_instances", len(filtered),
		"allowed_networks", r.config.MonitorDefaults.AllowedNetworks)

	return filtered
}

// sortNacosInstances 对Nacos实例进行排序
func (r *ServiceResolver) sortNacosInstances(instances []*nacos.ServiceInstance) {
	// 按IP:Port排序，确保实例顺序的一致性
	for i := 0; i < len(instances)-1; i++ {
		for j := i + 1; j < len(instances); j++ {
			instanceI := fmt.Sprintf("%s:%d", instances[i].IP, instances[i].Port)
			instanceJ := fmt.Sprintf("%s:%d", instances[j].IP, instances[j].Port)
			if instanceI > instanceJ {
				instances[i], instances[j] = instances[j], instances[i]
			}
		}
	}
}

// validateInstanceConfig 验证实例配置
func (r *ServiceResolver) validateInstanceConfig(config *ServiceConfig) error {
	if config.Name == "" {
		return fmt.Errorf("服务名称不能为空")
	}

	if config.IsMultiInstanceEnabled() && config.InstanceID == "" {
		return fmt.Errorf("多实例服务必须有实例ID")
	}

	if config.Endpoint == "" {
		return fmt.Errorf("服务端点不能为空")
	}

	return nil
}

// generateInstanceID 生成实例ID
func (r *ServiceResolver) generateInstanceID(ip string, port uint64) string {
	return fmt.Sprintf("%s:%d", ip, port)
}

// buildInstanceEndpoint 构建实例端点
func (r *ServiceResolver) buildInstanceEndpoint(ip string, port uint64, healthPath string) string {
	if healthPath == "" {
		healthPath = r.config.MonitorDefaults.HealthPath
	}
	return fmt.Sprintf("http://%s:%d%s", ip, port, healthPath)
}
