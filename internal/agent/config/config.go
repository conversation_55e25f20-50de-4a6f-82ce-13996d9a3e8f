package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	"gopkg.in/yaml.v3"
)

// Config Agent配置结构
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Security SecurityConfig `yaml:"security"`
	Docker   DockerConfig   `yaml:"docker"`
	System   SystemConfig   `yaml:"system"`
	Logging  LoggingConfig  `yaml:"logging"`
	SMS      SMSConfig      `yaml:"sms"`
}

// ServerConfig HTTP服务器配置
type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	Token     string `yaml:"token"`
	EnableTLS bool   `yaml:"enable_tls"`
	CertFile  string `yaml:"cert_file"`
	KeyFile   string `yaml:"key_file"`
}

// DockerConfig Docker配置
type DockerConfig struct {
	SocketPath string `yaml:"socket_path"`
	Timeout    int    `yaml:"timeout"`
}

// SystemConfig 系统监控配置
type SystemConfig struct {
	CollectInterval int `yaml:"collect_interval"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
}

// SMSConfig 短信服务配置
type SMSConfig struct {
	Enabled bool `yaml:"enabled"` // 是否启用短信功能
}

// Load 加载配置文件
func Load() (*Config, error) {
	configPath := filepath.Join("configs", "agent", "agent.yml")

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 从环境变量覆盖配置
	loadFromEnv(&cfg)

	return &cfg, nil
}

// loadFromEnv 从环境变量加载配置
func loadFromEnv(cfg *Config) {
	if token := os.Getenv("AGENT_TOKEN"); token != "" {
		cfg.Security.Token = token
	}

	if host := os.Getenv("AGENT_HOST"); host != "" {
		cfg.Server.Host = host
	}

	if portStr := os.Getenv("AGENT_PORT"); portStr != "" {
		if port, err := strconv.Atoi(portStr); err == nil {
			cfg.Server.Port = port
		}
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.Logging.Level = logLevel
	}

	if smsEnabled := os.Getenv("SMS_ENABLED"); smsEnabled == "false" {
		cfg.SMS.Enabled = false
	}
}
