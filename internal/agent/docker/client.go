package docker

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"monitor/internal/shared/logger"

	"github.com/docker/docker/api/types"
	containertypes "github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
)

// Client Docker客户端封装
type Client struct {
	client *client.Client
}

// ContainerStatus 容器状态信息
type ContainerStatus struct {
	ID      string    `json:"id"`
	Name    string    `json:"name"`
	Status  string    `json:"status"`
	State   string    `json:"state"`
	Created time.Time `json:"created"`
	Image   string    `json:"image"`
}

// NewClient 创建Docker客户端
func NewClient() (*Client, error) {
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return nil, fmt.Errorf("创建Docker客户端失败: %w", err)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = cli.Ping(ctx)
	if err != nil {
		return nil, fmt.Errorf("连接Docker守护进程失败: %w", err)
	}

	logger.Info("Docker客户端初始化成功")
	return &Client{client: cli}, nil
}

// Close 关闭Docker客户端
func (c *Client) Close() error {
	if c.client != nil {
		return c.client.Close()
	}
	return nil
}

// RestartContainer 重启容器
func (c *Client) RestartContainer(containerName string) error {
	logger.Info("Attempting to restart container", "container", containerName)

	// 查找容器
	container, err := c.findContainer(containerName)
	if err != nil {
		return err
	}

	// 重启容器
	timeout := 30
	err = c.client.ContainerRestart(context.Background(), container.ID, containertypes.StopOptions{
		Timeout: &timeout,
	})
	if err != nil {
		return fmt.Errorf("重启容器 %s 失败: %w", containerName, err)
	}

	logger.Info("容器重启成功", "container", containerName)
	return nil
}

// GetContainerStatus 获取容器状态
func (c *Client) GetContainerStatus(containerName string) (*ContainerStatus, error) {
	container, err := c.findContainer(containerName)
	if err != nil {
		return nil, err
	}

	return &ContainerStatus{
		ID:      container.ID,
		Name:    containerName,
		Status:  container.Status,
		State:   container.State,
		Created: time.Unix(container.Created, 0),
		Image:   container.Image,
	}, nil
}

// GetContainerLogs 获取容器日志
func (c *Client) GetContainerLogs(containerName string, lines int) ([]string, error) {
	container, err := c.findContainer(containerName)
	if err != nil {
		return nil, err
	}

	// 获取日志
	// 获取容器日志
	tail := fmt.Sprintf("%d", lines)
	options := containertypes.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Tail:       tail,
		Timestamps: true,
	}

	logs, err := c.client.ContainerLogs(context.Background(), container.ID, options)
	if err != nil {
		return nil, fmt.Errorf("获取容器日志失败: %w", err)
	}
	defer logs.Close()

	// 读取日志内容
	logData, err := io.ReadAll(logs)
	if err != nil {
		return nil, fmt.Errorf("读取日志数据失败: %w", err)
	}

	// 分割日志行
	logLines := strings.Split(string(logData), "\n")

	// 过滤空行
	var result []string
	for _, line := range logLines {
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}

	return result, nil
}

// ListContainers 列出所有容器
func (c *Client) ListContainers() ([]ContainerStatus, error) {
	containers, err := c.client.ContainerList(context.Background(), containertypes.ListOptions{
		All: true,
	})
	if err != nil {
		return nil, fmt.Errorf("列出容器失败: %w", err)
	}

	var result []ContainerStatus
	for _, container := range containers {
		name := "unknown"
		if len(container.Names) > 0 {
			name = strings.TrimPrefix(container.Names[0], "/")
		}

		result = append(result, ContainerStatus{
			ID:      container.ID,
			Name:    name,
			Status:  container.Status,
			State:   container.State,
			Created: time.Unix(container.Created, 0),
			Image:   container.Image,
		})
	}

	return result, nil
}

// findContainer 查找容器
func (c *Client) findContainer(containerName string) (*types.Container, error) {
	containers, err := c.client.ContainerList(context.Background(), containertypes.ListOptions{
		All: true,
	})
	if err != nil {
		return nil, fmt.Errorf("列出容器失败: %w", err)
	}

	for _, container := range containers {
		for _, name := range container.Names {
			// Docker容器名称前面有"/"前缀
			if strings.TrimPrefix(name, "/") == containerName {
				return &container, nil
			}
		}
	}

	return nil, fmt.Errorf("容器 %s 未找到", containerName)
}

// IsContainerHealthy 检查容器是否健康
func (c *Client) IsContainerHealthy(containerName string) (bool, error) {
	status, err := c.GetContainerStatus(containerName)
	if err != nil {
		return false, err
	}

	// 检查容器是否在运行
	return status.State == "running", nil
}
