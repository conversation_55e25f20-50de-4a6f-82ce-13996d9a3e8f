package sms

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"monitor/internal/shared/logger"
)

// SMSRecord 短信记录
type SMSRecord struct {
	ID           string    `json:"id"`            // 短信ID
	Account      string    `json:"account"`       // 账号
	Phone        string    `json:"phone"`         // 手机号
	Content      string    `json:"content"`       // 短信内容
	SendTime     string    `json:"send_time"`     // 发送时间
	Status       string    `json:"status"`        // 状态：DELIVRD(已送达), UNDELIV(未送达), UNKNOWN(未知)
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
	ResponseCode int       `json:"response_code"` // 响应码
}

// SendSMSRequest 发送短信请求
type SendSMSRequest struct {
	Account  string `json:"account"`
	Password string `json:"password"`
	Phone    string `json:"phone"`
	Content  string `json:"content"`
	SendTime string `json:"send_time"`
}

// SendSMSResponse 发送短信响应
type SendSMSResponse struct {
	Response int    `json:"response"`
	Phone    string `json:"phone"`
	SMSID    string `json:"sms_id"`
}

// GetStatusRequest 查询状态请求
type GetStatusRequest struct {
	Account  string `json:"account"`
	Password string `json:"password"`
	SmsID    string `json:"sms_id"`
}

// GetStatusResponse 查询状态响应
type GetStatusResponse struct {
	Response int    `json:"response"`
	SMSID    string `json:"sms_id"`
	Phone    string `json:"phone"`
	Status   string `json:"status"`
}

// SMSSimulator 短信模拟器
type SMSSimulator struct {
	records   sync.Map     // 存储短信记录，key为短信ID，value为*SMSRecord
	idCounter int64        // ID计数器，用于生成唯一ID
	mu        sync.RWMutex // 读写锁
}

// NewSMSSimulator 创建短信模拟器实例
func NewSMSSimulator() *SMSSimulator {
	return &SMSSimulator{
		records:   sync.Map{},
		idCounter: 0,
	}
}

// SendSMS 发送短信
func (s *SMSSimulator) SendSMS(req *SendSMSRequest) *SendSMSResponse {
	logger.Info("SMS simulator received send request",
		"account", req.Account,
		"phone", req.Phone,
		"content_length", len(req.Content))

	// 验证参数
	if code := s.validateSendParams(req); code != 0 {
		return &SendSMSResponse{
			Response: code,
			Phone:    req.Phone,
			SMSID:    "",
		}
	}

	// 验证密码（模拟MD5验证）
	if !s.validatePassword(req.Account, req.Password) {
		return &SendSMSResponse{
			Response: -1, // 账号不存在，请检查用户名或密码是否正确
			Phone:    req.Phone,
			SMSID:    "",
		}
	}

	// 生成短信ID
	smsID := s.generateSMSID()

	// 解码内容（模拟URL解码）
	content, err := url.QueryUnescape(req.Content)
	if err != nil {
		content = req.Content // 如果解码失败，使用原内容
	}

	// 创建短信记录
	record := &SMSRecord{
		ID:           smsID,
		Account:      req.Account,
		Phone:        req.Phone,
		Content:      content,
		SendTime:     req.SendTime,
		Status:       "DELIVRD", // 模拟成功送达
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		ResponseCode: 1, // 成功
	}

	// 存储记录
	s.records.Store(smsID, record)

	// 计算成功发送的手机号数量
	phoneCount := len(strings.Split(req.Phone, ","))

	logger.Info("短信发送成功",
		"sms_id", smsID,
		"phone_count", phoneCount,
		"account", req.Account)

	return &SendSMSResponse{
		Response: phoneCount, // 返回成功条数
		Phone:    req.Phone,
		SMSID:    smsID,
	}
}

// GetStatus 查询短信状态
func (s *SMSSimulator) GetStatus(req *GetStatusRequest) *GetStatusResponse {
	logger.Info("SMS simulator received status query",
		"account", req.Account,
		"sms_id", req.SmsID)

	// 验证参数
	if code := s.validateStatusParams(req); code != 0 {
		return &GetStatusResponse{
			Response: code,
			SMSID:    req.SmsID,
			Phone:    "",
			Status:   "null",
		}
	}

	// 验证密码
	if !s.validatePassword(req.Account, req.Password) {
		return &GetStatusResponse{
			Response: -1, // 账号不存在
			SMSID:    req.SmsID,
			Phone:    "",
			Status:   "null",
		}
	}

	// 查找记录
	if value, exists := s.records.Load(req.SmsID); exists {
		record := value.(*SMSRecord)
		return &GetStatusResponse{
			Response: 1, // 成功
			SMSID:    record.ID,
			Phone:    record.Phone,
			Status:   record.Status,
		}
	}

	// 记录不存在
	return &GetStatusResponse{
		Response: -14, // 服务内部错误（记录不存在）
		SMSID:    req.SmsID,
		Phone:    "",
		Status:   "null",
	}
}

// generateSMSID 生成唯一的短信ID
func (s *SMSSimulator) generateSMSID() string {
	// 使用时间戳 + 原子计数器生成唯一ID
	timestamp := time.Now().Format("************") // YYMMDDHHMMSS
	counter := atomic.AddInt64(&s.idCounter, 1)
	return fmt.Sprintf("%s%06d", timestamp, counter%1000000)
}

// validateSendParams 验证发送短信参数
func (s *SMSSimulator) validateSendParams(req *SendSMSRequest) int {
	// 检查必需参数
	if req.Account == "" || req.Password == "" || req.Phone == "" || req.Content == "" {
		return -8 // 缺少请求参数或参数名称不正确
	}

	// 验证手机号格式
	if !s.validatePhoneNumbers(req.Phone) {
		return -9 // 内容不合法
	}

	// 检查手机号数量限制
	phones := strings.Split(req.Phone, ",")
	if len(phones) > 100 {
		return -12 // 超过单次发送上限
	}

	// 检查内容长度（模拟限制）
	if len(req.Content) > 1000 {
		return -9 // 内容不合法
	}

	return 0 // 验证通过
}

// validateStatusParams 验证查询状态参数
func (s *SMSSimulator) validateStatusParams(req *GetStatusRequest) int {
	if req.Account == "" || req.Password == "" || req.SmsID == "" {
		return -8 // 缺少请求参数或参数名称不正确
	}
	return 0
}

// validatePassword 验证密码（模拟MD5验证）
func (s *SMSSimulator) validatePassword(account, password string) bool {
	// 模拟账号密码验证
	// 在真实环境中，这里应该查询数据库或配置文件
	expectedPasswords := map[string]string{
		"test":    "b9086c5af65dc0f42bcc61e7dd89a624", // MD5("test123")
		"admin":   "21232f297a57a5a743894a0e4a801fc3", // MD5("admin")
		"monitor": "5d41402abc4b2a76b9719d911017c592", // MD5("hello")
	}

	expected, exists := expectedPasswords[account]
	if !exists {
		return false
	}

	return password == expected
}

// validatePhoneNumbers 验证手机号格式
func (s *SMSSimulator) validatePhoneNumbers(phones string) bool {
	phoneList := strings.Split(phones, ",")
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`) // 中国手机号格式

	for _, phone := range phoneList {
		phone = strings.TrimSpace(phone)
		if !phoneRegex.MatchString(phone) {
			return false
		}
	}
	return true
}

// GetRecordCount 获取记录总数（用于测试）
func (s *SMSSimulator) GetRecordCount() int {
	count := 0
	s.records.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// ClearRecords 清空所有记录（用于测试）
func (s *SMSSimulator) ClearRecords() {
	s.records.Range(func(key, value interface{}) bool {
		s.records.Delete(key)
		return true
	})
	atomic.StoreInt64(&s.idCounter, 0)
}
