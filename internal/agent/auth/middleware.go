package auth

import (
	"net/http"
	"strings"
	"time"

	"monitor/internal/agent/config"
	"monitor/internal/shared/logger"

	"github.com/gin-gonic/gin"
)

// Middleware 认证中间件
type Middleware struct {
	config *config.Config
}

// NewMiddleware 创建认证中间件
func NewMiddleware(cfg *config.Config) *Middleware {
	return &Middleware{
		config: cfg,
	}
}

// AuthMiddleware Token认证中间件
func (m *Middleware) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 健康检查接口、短信接口和邮件接口不需要认证
		if c.Request.URL.Path == "/api/v1/health" ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/sms/") ||
			strings.HasPrefix(c.Request.URL.Path, "/email-endpoint/") {
			c.Next()
			return
		}

		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Warn("缺少授权头",
				"client_ip", c.Client<PERSON>(),
				"path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少授权令牌",
				"code":  "MISSING_TOKEN",
			})
			c.Abort()
			return
		}

		// 验证Bearer token格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logger.Warn("Invalid token format",
				"client_ip", c.ClientIP(),
				"path", c.Request.URL.Path,
				"auth_header", authHeader)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token format, expected 'Bearer <token>'",
				"code":  "INVALID_TOKEN_FORMAT",
			})
			c.Abort()
			return
		}

		// 提取token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			logger.Warn("Empty token",
				"client_ip", c.ClientIP(),
				"path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Empty token",
				"code":  "EMPTY_TOKEN",
			})
			c.Abort()
			return
		}

		// 验证token
		if token != m.config.Security.Token {
			logger.Warn("无效的令牌",
				"client_ip", c.ClientIP(),
				"path", c.Request.URL.Path,
				"provided_token", maskToken(token))
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的令牌",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// 认证成功，记录日志
		logger.Debug("认证成功",
			"client_ip", c.ClientIP(),
			"path", c.Request.URL.Path)

		c.Next()
	}
}

// LoggingMiddleware 日志中间件
func (m *Middleware) LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 记录请求日志
		duration := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		// 根据状态码选择日志级别
		if statusCode >= 400 {
			logger.Warn("HTTP请求完成（有错误）",
				"方法", method,
				"路径", path,
				"状态", statusCode,
				"持续时间", duration.String(),
				"客户端IP", clientIP,
				"用户代理", c.Request.UserAgent(),
			)
		} else {
			logger.Info("HTTP请求完成",
				"方法", method,
				"路径", path,
				"状态", statusCode,
				"持续时间", duration.String(),
				"客户端IP", clientIP,
			)
		}
	}
}

// CORSMiddleware CORS中间件
func (m *Middleware) CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// maskToken 遮蔽token用于日志记录
func maskToken(token string) string {
	if len(token) <= 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}
