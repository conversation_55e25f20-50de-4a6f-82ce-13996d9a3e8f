package system

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"monitor/internal/shared/logger"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
)

// SystemInfo 系统基本信息
type SystemInfo struct {
	Hostname        string    `json:"hostname"`
	Platform        string    `json:"platform"`
	PlatformFamily  string    `json:"platform_family"`
	PlatformVersion string    `json:"platform_version"`
	KernelVersion   string    `json:"kernel_version"`
	KernelArch      string    `json:"kernel_arch"`
	Uptime          uint64    `json:"uptime"`
	BootTime        time.Time `json:"boot_time"`
	Procs           uint64    `json:"procs"`
	GoVersion       string    `json:"go_version"`
	NumCPU          int       `json:"num_cpu"`
}

// SystemResources 系统资源使用情况
type SystemResources struct {
	CPU       CPUInfo     `json:"cpu"`
	Memory    MemoryInfo  `json:"memory"`
	Disk      []DiskInfo  `json:"disk"`
	Network   NetworkInfo `json:"network"`
	Timestamp time.Time   `json:"timestamp"`
}

// CPUInfo CPU信息
type CPUInfo struct {
	UsagePercent []float64 `json:"usage_percent"`
	AvgUsage     float64   `json:"avg_usage"`
}

// MemoryInfo 内存信息
type MemoryInfo struct {
	Total       uint64  `json:"total"`
	Available   uint64  `json:"available"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"used_percent"`
	Free        uint64  `json:"free"`
}

// DiskInfo 磁盘信息
type DiskInfo struct {
	Device      string  `json:"device"`
	Mountpoint  string  `json:"mountpoint"`
	Fstype      string  `json:"fstype"`
	Total       uint64  `json:"total"`
	Free        uint64  `json:"free"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"used_percent"`
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	BytesSent   uint64 `json:"bytes_sent"`
	BytesRecv   uint64 `json:"bytes_recv"`
	PacketsSent uint64 `json:"packets_sent"`
	PacketsRecv uint64 `json:"packets_recv"`
}

// DiskUsageInfo 磁盘使用情况信息
type DiskUsageInfo struct {
	Mountpoint     string               `json:"mountpoint"`
	Device         string               `json:"device"`
	Fstype         string               `json:"fstype"`
	Total          uint64               `json:"total"`
	Used           uint64               `json:"used"`
	Free           uint64               `json:"free"`
	UsedPercent    float64              `json:"used_percent"`
	Status         string               `json:"status"` // "normal", "warning", "critical"
	TopDirectories []DirectoryUsageItem `json:"top_directories,omitempty"`
	Timestamp      time.Time            `json:"timestamp"`
}

// DirectoryUsageItem 目录使用项
type DirectoryUsageItem struct {
	Name        string    `json:"name"`
	Path        string    `json:"path"`
	Size        uint64    `json:"size"`
	SizePercent float64   `json:"size_percent"` // 占总磁盘空间的百分比
	IsDir       bool      `json:"is_dir"`
	ModTime     time.Time `json:"mod_time"`
}

// DiskUsageAnalysis 磁盘使用分析
type DiskUsageAnalysis struct {
	AllDisks      []DiskUsageInfo `json:"all_disks"`
	CriticalDisks []DiskUsageInfo `json:"critical_disks"` // 使用率 >= 90%
	WarningDisks  []DiskUsageInfo `json:"warning_disks"`  // 使用率 >= 80%
	TotalDisks    int             `json:"total_disks"`
	HealthyDisks  int             `json:"healthy_disks"`
	WarningCount  int             `json:"warning_count"`
	CriticalCount int             `json:"critical_count"`
	OverallStatus string          `json:"overall_status"` // "healthy", "warning", "critical"
	Timestamp     time.Time       `json:"timestamp"`
}

// Collector 系统信息收集器
type Collector struct{}

// NewCollector 创建系统信息收集器
func NewCollector() *Collector {
	// 设置gopsutil使用宿主机路径
	setupHostPaths()
	return &Collector{}
}

// setupHostPaths 设置gopsutil使用宿主机路径
func setupHostPaths() {
	// 检查是否在容器环境中运行，如果环境变量存在则记录日志
	if hostProc := os.Getenv("HOST_PROC"); hostProc != "" {
		logger.Info("使用宿主机proc路径", "path", hostProc)
	}

	if hostSys := os.Getenv("HOST_SYS"); hostSys != "" {
		logger.Info("使用宿主机sys路径", "path", hostSys)
	}

	if hostRoot := os.Getenv("HOST_ROOT"); hostRoot != "" {
		logger.Info("使用宿主机root路径", "path", hostRoot)
	}

	if hostEtc := os.Getenv("HOST_ETC"); hostEtc != "" {
		logger.Info("使用宿主机etc路径", "path", hostEtc)
	}
}

// getHostHostname 从宿主机获取真实的hostname
func (c *Collector) getHostHostname() string {
	// 尝试多种方式获取宿主机hostname

	// 方法1: 从HOST_ETC环境变量指定的路径读取
	if hostEtc := os.Getenv("HOST_ETC"); hostEtc != "" {
		hostnameFile := hostEtc + "/hostname"
		if hostname := c.readHostnameFromFile(hostnameFile); hostname != "" {
			logger.Info("从HOST_ETC获取主机名", "hostname", hostname, "file", hostnameFile)
			return hostname
		}
	}

	// 方法2: 从HOST_ROOT环境变量指定的路径读取
	if hostRoot := os.Getenv("HOST_ROOT"); hostRoot != "" {
		hostnameFile := hostRoot + "/etc/hostname"
		if hostname := c.readHostnameFromFile(hostnameFile); hostname != "" {
			logger.Info("从HOST_ROOT获取主机名", "hostname", hostname, "file", hostnameFile)
			return hostname
		}
	}

	// 方法3: 尝试固定路径
	fixedPaths := []string{
		"/host/root/etc/hostname",
		"/host/etc/hostname",
	}

	for _, path := range fixedPaths {
		if hostname := c.readHostnameFromFile(path); hostname != "" {
			logger.Info("从固定路径获取主机名", "hostname", hostname, "file", path)
			return hostname
		}
	}

	logger.Warn("从宿主机系统获取主机名失败，使用容器主机名")
	return ""
}

// readHostnameFromFile 从文件读取hostname
func (c *Collector) readHostnameFromFile(filepath string) string {
	data, err := ioutil.ReadFile(filepath)
	if err != nil {
		logger.Debug("读取主机名文件失败", "file", filepath, "error", err)
		return ""
	}

	hostname := strings.TrimSpace(string(data))
	if hostname == "" {
		logger.Debug("文件中主机名为空", "file", filepath)
		return ""
	}

	return hostname
}

// GetSystemInfo 获取系统基本信息
func (c *Collector) GetSystemInfo() (*SystemInfo, error) {
	hostInfo, err := host.Info()
	if err != nil {
		return nil, fmt.Errorf("获取主机信息失败: %w", err)
	}

	// 尝试从宿主机获取真实的hostname
	hostname := c.getHostHostname()
	if hostname == "" {
		hostname = hostInfo.Hostname
	}

	return &SystemInfo{
		Hostname:        hostname,
		Platform:        hostInfo.Platform,
		PlatformFamily:  hostInfo.PlatformFamily,
		PlatformVersion: hostInfo.PlatformVersion,
		KernelVersion:   hostInfo.KernelVersion,
		KernelArch:      hostInfo.KernelArch,
		Uptime:          hostInfo.Uptime,
		BootTime:        time.Unix(int64(hostInfo.BootTime), 0),
		Procs:           hostInfo.Procs,
		GoVersion:       runtime.Version(),
		NumCPU:          runtime.NumCPU(),
	}, nil
}

// GetSystemResources 获取系统资源使用情况
func (c *Collector) GetSystemResources() (*SystemResources, error) {
	resources := &SystemResources{
		Timestamp: time.Now(),
	}

	// 获取CPU信息
	cpuInfo, err := c.getCPUInfo()
	if err != nil {
		logger.Warn("获取CPU信息失败", "error", err)
	} else {
		resources.CPU = *cpuInfo
	}

	// 获取内存信息
	memInfo, err := c.getMemoryInfo()
	if err != nil {
		logger.Warn("获取内存信息失败", "error", err)
	} else {
		resources.Memory = *memInfo
	}

	// 获取磁盘信息
	diskInfo, err := c.getDiskInfo()
	if err != nil {
		logger.Warn("获取磁盘信息失败", "error", err)
	} else {
		resources.Disk = diskInfo
	}

	// 获取网络信息
	netInfo, err := c.getNetworkInfo()
	if err != nil {
		logger.Warn("获取网络信息失败", "error", err)
	} else {
		resources.Network = *netInfo
	}

	return resources, nil
}

// getCPUInfo 获取CPU信息
func (c *Collector) getCPUInfo() (*CPUInfo, error) {
	// 获取CPU使用率
	percentages, err := cpu.Percent(time.Second, true)
	if err != nil {
		return nil, err
	}

	// 计算平均使用率
	var total float64
	for _, p := range percentages {
		total += p
	}
	avgUsage := total / float64(len(percentages))

	return &CPUInfo{
		UsagePercent: percentages,
		AvgUsage:     avgUsage,
	}, nil
}

// getMemoryInfo 获取内存信息
func (c *Collector) getMemoryInfo() (*MemoryInfo, error) {
	memStat, err := mem.VirtualMemory()
	if err != nil {
		return nil, err
	}

	// 尝试从 /proc/meminfo 获取更准确的内存信息
	memInfo, err := c.getMemoryInfoFromProc()
	if err != nil {
		logger.Warn("从/proc/meminfo获取内存信息失败，使用gopsutil数据", "error", err)
		// 使用 gopsutil 的数据作为备选
		return &MemoryInfo{
			Total:       memStat.Total,
			Available:   memStat.Available,
			Used:        memStat.Used,
			UsedPercent: memStat.UsedPercent,
			Free:        memStat.Free,
		}, nil
	}

	return memInfo, nil
}

// getMemoryInfoFromProc 从 /proc/meminfo 获取内存信息
func (c *Collector) getMemoryInfoFromProc() (*MemoryInfo, error) {
	// 构建 /proc/meminfo 路径
	meminfoPath := "/proc/meminfo"
	if hostProc := os.Getenv("HOST_PROC"); hostProc != "" {
		meminfoPath = filepath.Join(hostProc, "meminfo")
	}

	data, err := ioutil.ReadFile(meminfoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read %s: %w", meminfoPath, err)
	}

	// 解析 /proc/meminfo
	lines := strings.Split(string(data), "\n")
	memInfo := make(map[string]uint64)

	for _, line := range lines {
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 2 {
			continue
		}

		key := strings.TrimSuffix(parts[0], ":")
		var value uint64
		if len(parts) >= 2 {
			// 解析数值，/proc/meminfo 中的值通常以 kB 为单位
			if val, err := strconv.ParseUint(parts[1], 10, 64); err == nil {
				value = val * 1024 // 转换为字节
			}
		}
		memInfo[key] = value
	}

	// 计算内存信息（完全按照 free 命令的逻辑）
	total := memInfo["MemTotal"]
	free := memInfo["MemFree"]
	buffers := memInfo["Buffers"]
	cached := memInfo["Cached"]
	sReclaimable := memInfo["SReclaimable"]
	available := memInfo["MemAvailable"]

	// free 命令的真实计算方式（基于最终调试结果）：
	// buff/cache = buffers + cached + sReclaimable
	// used = total - free - buffers - cached - sReclaimable
	// 这是与 free 命令输出最匹配的计算方式（差异仅516KB）
	used := total - free - buffers - cached - sReclaimable

	// 如果没有 MemAvailable，则按照内核的计算方式估算
	if available == 0 {
		available = free + buffers + cached + sReclaimable
	}

	// 确保计算结果的合理性
	if used > total {
		used = total - available
	}
	if used < 0 {
		used = 0
	}

	// 计算使用百分比
	var usedPercent float64
	if total > 0 {
		usedPercent = float64(used) / float64(total) * 100
	}

	// 记录调试信息
	logger.Debug("内存计算详情（free兼容最终版）",
		"total", total,
		"free", free,
		"buffers", buffers,
		"cached", cached,
		"sReclaimable", sReclaimable,
		"available", available,
		"calculated_used", used,
		"used_percent", usedPercent)

	return &MemoryInfo{
		Total:       total,
		Available:   available,
		Used:        used,
		UsedPercent: usedPercent,
		Free:        free,
	}, nil
}

// getDiskInfo 获取磁盘信息
func (c *Collector) getDiskInfo() ([]DiskInfo, error) {
	partitions, err := disk.Partitions(false)
	if err != nil {
		return nil, err
	}

	var diskInfos []DiskInfo
	for _, partition := range partitions {
		usage, err := disk.Usage(partition.Mountpoint)
		if err != nil {
			logger.Warn("获取磁盘使用情况失败", "mountpoint", partition.Mountpoint, "error", err)
			continue
		}

		diskInfos = append(diskInfos, DiskInfo{
			Device:      partition.Device,
			Mountpoint:  partition.Mountpoint,
			Fstype:      partition.Fstype,
			Total:       usage.Total,
			Free:        usage.Free,
			Used:        usage.Used,
			UsedPercent: usage.UsedPercent,
		})
	}

	return diskInfos, nil
}

// GetDiskUsageAnalysis 获取磁盘使用分析
func (c *Collector) GetDiskUsageAnalysis(includeDirAnalysis bool, maxTopDirs int) (*DiskUsageAnalysis, error) {
	// 获取所有磁盘分区
	partitions, err := disk.Partitions(false)
	if err != nil {
		return nil, fmt.Errorf("获取磁盘分区失败: %w", err)
	}

	analysis := &DiskUsageAnalysis{
		Timestamp: time.Now(),
	}

	var allDisks []DiskUsageInfo
	var criticalDisks []DiskUsageInfo
	var warningDisks []DiskUsageInfo

	for _, partition := range partitions {
		// 跳过特殊文件系统
		if c.shouldSkipPartition(partition) {
			continue
		}

		usage, err := disk.Usage(partition.Mountpoint)
		if err != nil {
			logger.Warn("获取磁盘使用情况失败", "mountpoint", partition.Mountpoint, "error", err)
			continue
		}

		// 计算状态
		status := c.getDiskStatus(usage.UsedPercent)

		diskInfo := DiskUsageInfo{
			Mountpoint:  partition.Mountpoint,
			Device:      c.normalizeDeviceName(partition.Device),
			Fstype:      partition.Fstype,
			Total:       usage.Total,
			Used:        usage.Used,
			Free:        usage.Free,
			UsedPercent: usage.UsedPercent,
			Status:      status,
			Timestamp:   time.Now(),
		}

		// 如果需要目录分析且不是特殊挂载点
		if includeDirAnalysis && maxTopDirs > 0 && c.shouldAnalyzeDirectory(partition.Mountpoint) {
			topDirs, err := c.getTopDirectories(partition.Mountpoint, maxTopDirs, usage.Total)
			if err != nil {
				logger.Warn("获取顶级目录失败", "mountpoint", partition.Mountpoint, "error", err)
			} else {
				diskInfo.TopDirectories = topDirs
			}
		}

		allDisks = append(allDisks, diskInfo)

		// 分类磁盘状态
		switch status {
		case "critical":
			criticalDisks = append(criticalDisks, diskInfo)
		case "warning":
			warningDisks = append(warningDisks, diskInfo)
		}
	}

	// 统计信息
	analysis.AllDisks = allDisks
	analysis.CriticalDisks = criticalDisks
	analysis.WarningDisks = warningDisks
	analysis.TotalDisks = len(allDisks)
	analysis.CriticalCount = len(criticalDisks)
	analysis.WarningCount = len(warningDisks)
	analysis.HealthyDisks = analysis.TotalDisks - analysis.CriticalCount - analysis.WarningCount

	// 整体状态
	analysis.OverallStatus = c.getOverallStatus(analysis.CriticalCount, analysis.WarningCount)

	return analysis, nil
}

// shouldSkipPartition 判断是否应该跳过分区
func (c *Collector) shouldSkipPartition(partition disk.PartitionStat) bool {
	// 跳过特殊文件系统
	skipFsTypes := []string{
		"tmpfs", "devtmpfs", "proc", "sysfs", "devpts", "cgroup", "cgroup2",
		"pstore", "bpf", "tracefs", "debugfs", "mqueue", "hugetlbfs",
		"securityfs", "overlay", "squashfs", "iso9660",
	}

	for _, skipType := range skipFsTypes {
		if partition.Fstype == skipType {
			return true
		}
	}

	// 跳过特殊挂载点
	skipMountpoints := []string{
		"/dev", "/proc", "/sys", "/run",
	}

	for _, skipMount := range skipMountpoints {
		if strings.HasPrefix(partition.Mountpoint, skipMount) {
			return true
		}
	}

	// 特殊处理：只跳过 /boot/efi，但保留 /boot
	if partition.Mountpoint == "/boot/efi" {
		return true
	}

	return false
}

// getDiskStatus 根据使用率获取磁盘状态
func (c *Collector) getDiskStatus(usedPercent float64) string {
	if usedPercent >= 90.0 {
		return "critical"
	} else if usedPercent >= 80.0 {
		return "warning"
	}
	return "normal"
}

// shouldAnalyzeDirectory 判断是否应该分析目录
func (c *Collector) shouldAnalyzeDirectory(mountpoint string) bool {
	// 只分析主要的挂载点
	analyzeMountpoints := []string{"/", "/home", "/var", "/opt", "/usr", "/tmp", "/boot"}

	for _, analyzeMount := range analyzeMountpoints {
		if mountpoint == analyzeMount {
			return true
		}
	}

	return false
}

// getOverallStatus 获取整体状态
func (c *Collector) getOverallStatus(criticalCount, warningCount int) string {
	if criticalCount > 0 {
		return "critical"
	} else if warningCount > 0 {
		return "warning"
	}
	return "healthy"
}

// normalizeDeviceName 标准化设备名称
func (c *Collector) normalizeDeviceName(deviceName string) string {
	// 如果设备名称包含 /host/root/dev，则进行映射
	if strings.HasPrefix(deviceName, "/host/root/dev/") {
		// 移除 /host/root 前缀
		normalizedName := strings.TrimPrefix(deviceName, "/host/root")

		// 处理设备映射器名称
		if strings.HasPrefix(normalizedName, "/dev/dm-") {
			// 尝试从 /proc/mounts 或其他方式获取真实的设备名称
			if realName := c.getRealDeviceName(normalizedName); realName != "" {
				return realName
			}
		}

		return normalizedName
	}

	return deviceName
}

// getRealDeviceName 获取真实的设备名称
func (c *Collector) getRealDeviceName(dmDevice string) string {
	// 首先尝试从 /dev/mapper 目录查找对应的设备
	if mapperName := c.findMapperDevice(dmDevice); mapperName != "" {
		return mapperName
	}

	// 然后尝试从 /proc/mounts 读取
	mountsFiles := []string{
		"/host/root/proc/mounts",
		"/proc/mounts",
	}

	for _, mountsFile := range mountsFiles {
		if realName := c.parseDeviceFromMounts(mountsFile, dmDevice); realName != "" {
			return realName
		}
	}

	return ""
}

// parseDeviceFromMounts 从 /proc/mounts 解析设备名称
func (c *Collector) parseDeviceFromMounts(mountsFile, dmDevice string) string {
	data, err := ioutil.ReadFile(mountsFile)
	if err != nil {
		logger.Debug("读取mounts文件失败", "file", mountsFile, "error", err)
		return ""
	}

	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			device := fields[0]
			// 如果找到对应的dm设备，尝试映射到mapper名称
			if device == dmDevice {
				// 检查是否有对应的mapper设备
				if strings.Contains(line, "mapper") {
					// 从设备名称推断mapper名称
					if mapperName := c.inferMapperName(dmDevice); mapperName != "" {
						return mapperName
					}
				}
			}
		}
	}

	return ""
}

// findMapperDevice 查找对应的mapper设备
func (c *Collector) findMapperDevice(dmDevice string) string {
	// 尝试从宿主机的 /dev/mapper 目录查找
	mapperDirs := []string{
		"/host/root/dev/mapper",
		"/dev/mapper",
	}

	for _, mapperDir := range mapperDirs {
		if mapperName := c.scanMapperDir(mapperDir, dmDevice); mapperName != "" {
			return mapperName
		}
	}

	return ""
}

// scanMapperDir 扫描mapper目录查找对应设备
func (c *Collector) scanMapperDir(mapperDir, dmDevice string) string {
	entries, err := os.ReadDir(mapperDir)
	if err != nil {
		logger.Debug("读取mapper目录失败", "dir", mapperDir, "error", err)
		return ""
	}

	// 提取dm设备编号
	dmNumber := strings.TrimPrefix(dmDevice, "/dev/dm-")

	for _, entry := range entries {
		if entry.Name() == "control" {
			continue
		}

		mapperPath := filepath.Join(mapperDir, entry.Name())

		// 读取符号链接或检查设备号
		if target, err := os.Readlink(mapperPath); err == nil {
			if strings.Contains(target, "dm-"+dmNumber) {
				return "/dev/mapper/" + entry.Name()
			}
		}
	}

	return ""
}

// inferMapperName 推断mapper设备名称（备用方法）
func (c *Collector) inferMapperName(dmDevice string) string {
	// 常见的映射模式（作为备用）
	mapperMappings := map[string]string{
		"/dev/dm-0": "/dev/mapper/centos-root",
		"/dev/dm-1": "/dev/mapper/centos-swap",
		"/dev/dm-2": "/dev/mapper/centos-home",
	}

	if mapperName, exists := mapperMappings[dmDevice]; exists {
		return mapperName
	}

	return ""
}

// getTopDirectories 获取顶级目录使用情况
func (c *Collector) getTopDirectories(mountpoint string, maxCount int, totalDiskSize uint64) ([]DirectoryUsageItem, error) {
	entries, err := os.ReadDir(mountpoint)
	if err != nil {
		return nil, err
	}

	var topDirs []DirectoryUsageItem

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		dirPath := filepath.Join(mountpoint, entry.Name())
		info, err := entry.Info()
		if err != nil {
			continue
		}

		// 计算目录大小
		size, err := c.calculateDirectorySize(dirPath)
		if err != nil {
			continue
		}

		// 计算占磁盘总空间的百分比
		sizePercent := float64(size) / float64(totalDiskSize) * 100

		topDirs = append(topDirs, DirectoryUsageItem{
			Name:        entry.Name(),
			Path:        dirPath,
			Size:        size,
			SizePercent: sizePercent,
			IsDir:       true,
			ModTime:     info.ModTime(),
		})
	}

	// 按大小排序
	sort.Slice(topDirs, func(i, j int) bool {
		return topDirs[i].Size > topDirs[j].Size
	})

	// 限制返回数量
	if maxCount > 0 && len(topDirs) > maxCount {
		topDirs = topDirs[:maxCount]
	}

	return topDirs, nil
}

// calculateDirectorySize 计算目录大小
func (c *Collector) calculateDirectorySize(dirPath string) (uint64, error) {
	var totalSize uint64

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			// 跳过无法访问的文件/目录，但不中断整个过程
			return nil
		}

		if !info.IsDir() {
			totalSize += uint64(info.Size())
		}

		return nil
	})

	return totalSize, err
}

// getNetworkInfo 获取网络信息
func (c *Collector) getNetworkInfo() (*NetworkInfo, error) {
	netStats, err := net.IOCounters(false)
	if err != nil {
		return nil, err
	}

	if len(netStats) == 0 {
		return &NetworkInfo{}, nil
	}

	// 汇总所有网络接口的统计信息
	var totalBytesSent, totalBytesRecv, totalPacketsSent, totalPacketsRecv uint64
	for _, stat := range netStats {
		totalBytesSent += stat.BytesSent
		totalBytesRecv += stat.BytesRecv
		totalPacketsSent += stat.PacketsSent
		totalPacketsRecv += stat.PacketsRecv
	}

	return &NetworkInfo{
		BytesSent:   totalBytesSent,
		BytesRecv:   totalBytesRecv,
		PacketsSent: totalPacketsSent,
		PacketsRecv: totalPacketsRecv,
	}, nil
}
