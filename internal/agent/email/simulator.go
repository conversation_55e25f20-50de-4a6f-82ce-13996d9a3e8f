package email

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"monitor/internal/shared/logger"
)

// EmailSimulator 邮件发送模拟器
type EmailSimulator struct {
	records sync.Map // 存储邮件发送记录
	counter int64    // 原子计数器，用于生成唯一ID
}

// EmailRecord 邮件发送记录
type EmailRecord struct {
	ID        string    `json:"id"`         // 邮件ID
	To        string    `json:"to"`         // 收件人
	Subject   string    `json:"subject"`    // 邮件主题
	Content   string    `json:"content"`    // 邮件内容
	Status    string    `json:"status"`     // 发送状态
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// SendEmailRequest 发送邮件请求
type SendEmailRequest struct {
	To      string `json:"to"`      // 收件人邮箱
	Subject string `json:"subject"` // 邮件主题
	Content string `json:"content"` // 邮件内容
}

// SendEmailResponse 发送邮件响应
type SendEmailResponse struct {
	Code    int         `json:"code"`    // 响应状态码
	Success bool        `json:"success"` // 是否成功
	Data    interface{} `json:"data"`    // 响应数据
	Message string      `json:"msg"`     // 响应消息
}

// NewEmailSimulator 创建邮件模拟器
func NewEmailSimulator() *EmailSimulator {
	return &EmailSimulator{}
}

// SendEmail 发送邮件
func (s *EmailSimulator) SendEmail(req *SendEmailRequest) *SendEmailResponse {
	logger.Info("Email simulator received send request",
		"to", req.To,
		"subject", req.Subject,
		"content_length", len(req.Content))

	// 验证参数
	if code, msg := s.validateSendParams(req); code != 200 {
		return &SendEmailResponse{
			Code:    code,
			Success: false,
			Data:    map[string]interface{}{},
			Message: msg,
		}
	}

	// 生成邮件ID
	emailID := s.generateEmailID()

	// 创建邮件记录
	record := &EmailRecord{
		ID:        emailID,
		To:        req.To,
		Subject:   req.Subject,
		Content:   req.Content,
		Status:    "sent", // 模拟成功发送
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 存储记录
	s.records.Store(emailID, record)

	logger.Info("邮件发送成功",
		"email_id", emailID,
		"to", req.To,
		"subject", req.Subject)

	return &SendEmailResponse{
		Code:    200,
		Success: true,
		Data:    map[string]interface{}{},
		Message: "邮件发送成功",
	}
}

// GetEmailRecord 获取邮件记录
func (s *EmailSimulator) GetEmailRecord(emailID string) (*EmailRecord, bool) {
	if record, exists := s.records.Load(emailID); exists {
		return record.(*EmailRecord), true
	}
	return nil, false
}

// GetAllRecords 获取所有邮件记录
func (s *EmailSimulator) GetAllRecords() []*EmailRecord {
	var records []*EmailRecord
	s.records.Range(func(key, value interface{}) bool {
		records = append(records, value.(*EmailRecord))
		return true
	})
	return records
}

// validateSendParams 验证发送邮件参数
func (s *EmailSimulator) validateSendParams(req *SendEmailRequest) (int, string) {
	// 检查必需参数
	if req.To == "" {
		return 400, "收件人邮箱不能为空"
	}
	if req.Subject == "" {
		return 400, "邮件主题不能为空"
	}
	if req.Content == "" {
		return 400, "邮件内容不能为空"
	}

	// 验证邮箱格式（简单验证）
	if !s.validateEmailFormat(req.To) {
		return 400, "收件人邮箱格式不正确"
	}

	// 检查内容长度（模拟限制）
	if len(req.Content) > 10000 {
		return 400, "邮件内容过长"
	}

	if len(req.Subject) > 200 {
		return 400, "邮件主题过长"
	}

	return 200, "验证通过"
}

// validateEmailFormat 验证邮箱格式
func (s *EmailSimulator) validateEmailFormat(email string) bool {
	// 简单的邮箱格式验证
	if len(email) < 5 {
		return false
	}

	// 检查是否包含@符号
	atIndex := -1
	for i, char := range email {
		if char == '@' {
			if atIndex != -1 {
				return false // 多个@符号
			}
			atIndex = i
		}
	}

	if atIndex <= 0 || atIndex >= len(email)-1 {
		return false // @符号位置不正确
	}

	// 检查@符号后是否有点号
	dotFound := false
	for i := atIndex + 1; i < len(email); i++ {
		if email[i] == '.' {
			dotFound = true
			break
		}
	}

	return dotFound
}

// generateEmailID 生成邮件ID
func (s *EmailSimulator) generateEmailID() string {
	now := time.Now()
	counter := atomic.AddInt64(&s.counter, 1)
	return fmt.Sprintf("EMAIL_%d%02d%02d%02d%02d%02d%03d_%d",
		now.Year(), now.Month(), now.Day(),
		now.Hour(), now.Minute(), now.Second(),
		now.Nanosecond()/1000000, counter)
}

// ClearRecords 清空所有记录（用于测试）
func (s *EmailSimulator) ClearRecords() {
	s.records = sync.Map{}
	logger.Info("已清空所有邮件记录")
}

// GetRecordCount 获取记录数量
func (s *EmailSimulator) GetRecordCount() int {
	count := 0
	s.records.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}
