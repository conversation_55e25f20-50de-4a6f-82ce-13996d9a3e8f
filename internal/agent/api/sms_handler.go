package api

import (
	"encoding/xml"
	"fmt"
	"net/http"
	"strings"

	"monitor/internal/agent/sms"
	"monitor/internal/shared/logger"

	"github.com/gin-gonic/gin"
)

// SMSHandler 短信API处理器
type SMSHandler struct {
	simulator *sms.SMSSimulator
}

// NewSMSHandler 创建短信API处理器
func NewSMSHandler() *SMSHandler {
	return &SMSHandler{
		simulator: sms.NewSMSSimulator(),
	}
}

// SendSMSXMLResponse 发送短信XML响应结构
type SendSMSXMLResponse struct {
	XMLName  xml.Name `xml:"result"`
	Response int      `xml:"response"`
	SMS      *SMSInfo `xml:"sms,omitempty"`
}

// GetStatusXMLResponse 查询状态XML响应结构
type GetStatusXMLResponse struct {
	XMLName  xml.Name       `xml:"result"`
	Response int            `xml:"response"`
	SMS      *SMSStatusInfo `xml:"sms,omitempty"`
}

// SMSInfo 短信信息
type SMSInfo struct {
	Phone string `xml:"phone"`
	SMSID string `xml:"smsID"`
}

// SMSStatusInfo 短信状态信息
type SMSStatusInfo struct {
	SMSID string `xml:"smsID"`
	Phone string `xml:"phone"`
	Stat  string `xml:"stat"`
}

// SendSMS 发送短信接口
// 支持GET和POST请求
// GET: /api/v1/sms/send?Account=test&Password=xxx&Phone=xxx&Content=xxx&SendTime=
// POST: /api/v1/sms/send (表单数据)
func (h *SMSHandler) SendSMS(c *gin.Context) {
	logger.Info("收到短信发送请求", "method", c.Request.Method, "remote_addr", c.ClientIP())

	// 解析参数（支持GET和POST）
	req, err := h.parseSendSMSParams(c)
	if err != nil {
		logger.Error("解析短信发送参数失败", "error", err)
		h.respondWithXMLError(c, -8) // 缺少请求参数或参数名称不正确
		return
	}

	// 调用模拟器发送短信
	response := h.simulator.SendSMS(req)

	// 生成XML响应
	xmlResponse := &SendSMSXMLResponse{
		Response: response.Response,
	}

	// 如果发送成功，添加短信信息
	if response.Response > 0 && response.SMSID != "" {
		xmlResponse.SMS = &SMSInfo{
			Phone: response.Phone,
			SMSID: response.SMSID,
		}
	}

	// 返回XML响应
	c.Header("Content-Type", "application/xml; charset=UTF-8")
	c.XML(http.StatusOK, xmlResponse)

	logger.Info("SMS send request processed",
		"response_code", response.Response,
		"sms_id", response.SMSID,
		"phone", response.Phone)
}

// GetStatus 查询短信状态接口
// GET: /api/v1/sms/status?Account=test&Password=xxx&SmsId=xxx
func (h *SMSHandler) GetStatus(c *gin.Context) {
	logger.Info("收到短信状态查询请求", "remote_addr", c.ClientIP())

	// 解析参数
	req, err := h.parseGetStatusParams(c)
	if err != nil {
		logger.Error("解析短信状态参数失败", "error", err)
		h.respondWithXMLError(c, -8) // 缺少请求参数或参数名称不正确
		return
	}

	// 调用模拟器查询状态
	response := h.simulator.GetStatus(req)

	// 生成XML响应
	xmlResponse := &GetStatusXMLResponse{
		Response: response.Response,
	}

	// 如果查询成功，添加状态信息
	if response.Response > 0 {
		xmlResponse.SMS = &SMSStatusInfo{
			SMSID: response.SMSID,
			Phone: response.Phone,
			Stat:  response.Status,
		}
	}

	// 返回XML响应
	c.Header("Content-Type", "application/xml; charset=UTF-8")
	c.XML(http.StatusOK, xmlResponse)

	logger.Info("SMS status query processed",
		"response_code", response.Response,
		"sms_id", response.SMSID,
		"status", response.Status)
}

// parseSendSMSParams 解析发送短信参数
func (h *SMSHandler) parseSendSMSParams(c *gin.Context) (*sms.SendSMSRequest, error) {
	var account, password, phone, content, sendTime string

	// 根据请求方法解析参数
	if c.Request.Method == "GET" {
		account = c.Query("Account")
		password = c.Query("Password")
		phone = c.Query("Phone")
		content = c.Query("Content")
		sendTime = c.Query("SendTime")
	} else if c.Request.Method == "POST" {
		// 解析表单数据
		if err := c.Request.ParseForm(); err != nil {
			return nil, fmt.Errorf("failed to parse form: %w", err)
		}
		account = c.PostForm("Account")
		password = c.PostForm("Password")
		phone = c.PostForm("Phone")
		content = c.PostForm("Content")
		sendTime = c.PostForm("SendTime")
	} else {
		return nil, fmt.Errorf("unsupported method: %s", c.Request.Method)
	}

	// 验证必需参数
	if account == "" {
		return nil, fmt.Errorf("missing Account parameter")
	}
	if password == "" {
		return nil, fmt.Errorf("missing Password parameter")
	}
	if phone == "" {
		return nil, fmt.Errorf("missing Phone parameter")
	}
	if content == "" {
		return nil, fmt.Errorf("missing Content parameter")
	}

	return &sms.SendSMSRequest{
		Account:  strings.TrimSpace(account),
		Password: strings.TrimSpace(password),
		Phone:    strings.TrimSpace(phone),
		Content:  content, // 内容不trim，保持原样
		SendTime: strings.TrimSpace(sendTime),
	}, nil
}

// parseGetStatusParams 解析查询状态参数
func (h *SMSHandler) parseGetStatusParams(c *gin.Context) (*sms.GetStatusRequest, error) {
	account := c.Query("Account")
	password := c.Query("Password")
	smsID := c.Query("SmsId") // 注意：文档中是SmsId，不是SmsID

	// 验证必需参数
	if account == "" {
		return nil, fmt.Errorf("missing Account parameter")
	}
	if password == "" {
		return nil, fmt.Errorf("missing Password parameter")
	}
	if smsID == "" {
		return nil, fmt.Errorf("missing SmsId parameter")
	}

	return &sms.GetStatusRequest{
		Account:  strings.TrimSpace(account),
		Password: strings.TrimSpace(password),
		SmsID:    strings.TrimSpace(smsID),
	}, nil
}

// respondWithXMLError 返回XML错误响应
func (h *SMSHandler) respondWithXMLError(c *gin.Context, errorCode int) {
	xmlResponse := &SendSMSXMLResponse{
		Response: errorCode,
	}

	c.Header("Content-Type", "application/xml; charset=UTF-8")

	// 手动构建XML响应以包含XML声明
	xmlData, err := xml.MarshalIndent(xmlResponse, "", "  ")
	if err != nil {
		logger.Error("序列化XML错误响应失败", "error", err)
		c.String(http.StatusInternalServerError, "内部服务器错误")
		return
	}

	// 添加XML声明头
	fullXML := `<?xml version="1.0" encoding="UTF-8"?>` + "\n" + string(xmlData)
	c.String(http.StatusOK, fullXML)
}

// GetSimulator 获取模拟器实例（用于测试）
func (h *SMSHandler) GetSimulator() *sms.SMSSimulator {
	return h.simulator
}

// ResetSimulator 重置模拟器（用于测试）
func (h *SMSHandler) ResetSimulator() {
	h.simulator.ClearRecords()
}
