package api

import (
	"net/http"

	"monitor/internal/agent/email"
	"monitor/internal/shared/logger"

	"github.com/gin-gonic/gin"
)

// EmailHandler 邮件API处理器
type EmailHandler struct {
	simulator *email.EmailSimulator
}

// NewEmailHandler 创建邮件API处理器
func NewEmailHandler() *EmailHandler {
	return &EmailHandler{
		simulator: email.NewEmailSimulator(),
	}
}

// SendEmail 发送邮件接口
// POST: /email-endpoint/send-simple (表单数据)
// 参数: to(收件人), subject(主题), content(内容)
func (h *EmailHandler) SendEmail(c *gin.Context) {
	logger.Info("收到邮件发送请求", "method", c.Request.Method, "remote_addr", c.ClientIP())

	// 解析表单参数
	req, err := h.parseSendEmailParams(c)
	if err != nil {
		logger.Error("解析邮件发送参数失败", "error", err)
		h.respondWithError(c, 400, "参数解析失败: "+err.Error())
		return
	}

	// 调用模拟器发送邮件
	response := h.simulator.SendEmail(req)

	// 返回JSON响应
	c.JSON(http.StatusOK, response)

	logger.Info("邮件发送请求处理完成",
		"to", req.To,
		"subject", req.Subject,
		"success", response.Success,
		"code", response.Code)
}

// GetEmailRecord 获取邮件记录接口
// GET: /email-endpoint/record/:id
func (h *EmailHandler) GetEmailRecord(c *gin.Context) {
	emailID := c.Param("id")
	if emailID == "" {
		h.respondWithError(c, 400, "邮件ID不能为空")
		return
	}

	record, exists := h.simulator.GetEmailRecord(emailID)
	if !exists {
		h.respondWithError(c, 404, "邮件记录不存在")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"success": true,
		"data":    record,
		"msg":     "获取邮件记录成功",
	})
}

// GetAllEmailRecords 获取所有邮件记录接口
// GET: /email-endpoint/records
func (h *EmailHandler) GetAllEmailRecords(c *gin.Context) {
	records := h.simulator.GetAllRecords()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"success": true,
		"data": gin.H{
			"records": records,
			"count":   len(records),
		},
		"msg": "获取邮件记录列表成功",
	})
}

// ClearEmailRecords 清空邮件记录接口
// DELETE: /email-endpoint/records
func (h *EmailHandler) ClearEmailRecords(c *gin.Context) {
	h.simulator.ClearRecords()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"success": true,
		"data":    gin.H{},
		"msg":     "清空邮件记录成功",
	})
}

// parseSendEmailParams 解析发送邮件参数
func (h *EmailHandler) parseSendEmailParams(c *gin.Context) (*email.SendEmailRequest, error) {
	// 从表单数据中获取参数
	to := c.PostForm("to")
	subject := c.PostForm("subject")
	content := c.PostForm("content")

	// 如果表单数据为空，尝试从JSON中获取
	if to == "" && subject == "" && content == "" {
		var jsonReq email.SendEmailRequest
		if err := c.ShouldBindJSON(&jsonReq); err == nil {
			to = jsonReq.To
			subject = jsonReq.Subject
			content = jsonReq.Content
		}
	}

	// 如果仍然为空，尝试从查询参数中获取
	if to == "" {
		to = c.Query("to")
	}
	if subject == "" {
		subject = c.Query("subject")
	}
	if content == "" {
		content = c.Query("content")
	}

	return &email.SendEmailRequest{
		To:      to,
		Subject: subject,
		Content: content,
	}, nil
}

// respondWithError 返回错误响应
func (h *EmailHandler) respondWithError(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, gin.H{
		"code":    code,
		"success": false,
		"data":    gin.H{},
		"msg":     message,
	})
}

// GetSimulator 获取模拟器实例（用于测试）
func (h *EmailHandler) GetSimulator() *email.EmailSimulator {
	return h.simulator
}
