package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"monitor/internal/agent/auth"
	"monitor/internal/agent/config"
	"monitor/internal/agent/docker"
	"monitor/internal/agent/system"
	"monitor/internal/shared/logger"

	"github.com/gin-gonic/gin"
)

// Handler API处理器
type Handler struct {
	config          *config.Config
	dockerClient    *docker.Client
	systemCollector *system.Collector
	authMiddleware  *auth.Middleware
	smsHandler      *SMSHandler
	emailHandler    *EmailHandler
}

// NewHandler 创建API处理器
func NewHandler(cfg *config.Config) *Handler {
	// 初始化Docker客户端
	dockerClient, err := docker.NewClient()
	if err != nil {
		logger.Warn("初始化Docker客户端失败，Docker功能将被禁用", "error", err)
		dockerClient = nil
	}

	// 初始化系统信息收集器
	systemCollector := system.NewCollector()

	// 初始化认证中间件
	authMiddleware := auth.NewMiddleware(cfg)

	// 初始化短信处理器（如果启用）
	var smsHandler *SMSHandler
	if cfg.SMS.Enabled {
		smsHandler = NewSMSHandler()
		logger.Info("短信处理器已初始化")
	} else {
		logger.Info("短信功能已禁用")
	}

	// 初始化邮件处理器（默认启用）
	emailHandler := NewEmailHandler()
	logger.Info("邮件处理器已初始化")

	return &Handler{
		config:          cfg,
		dockerClient:    dockerClient,
		systemCollector: systemCollector,
		authMiddleware:  authMiddleware,
		smsHandler:      smsHandler,
		emailHandler:    emailHandler,
	}
}

// SetupRoutes 设置路由
func (h *Handler) SetupRoutes() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)
	r := gin.New()

	// 添加中间件
	r.Use(gin.Recovery())
	r.Use(h.authMiddleware.LoggingMiddleware())
	r.Use(h.authMiddleware.CORSMiddleware())
	r.Use(h.authMiddleware.AuthMiddleware())

	// API路由组
	v1 := r.Group("/api/v1")
	{
		// 健康检查
		v1.GET("/health", h.HealthCheck)

		// Docker管理
		docker := v1.Group("/docker")
		{
			docker.POST("/containers/:name/restart", h.RestartContainer)
			docker.GET("/containers/:name/status", h.GetContainerStatus)
			docker.GET("/containers/:name/logs", h.GetContainerLogs)
			docker.GET("/containers", h.ListContainers)
		}

		// 系统信息
		system := v1.Group("/system")
		{
			system.GET("/info", h.GetSystemInfo)
			system.GET("/resources", h.GetSystemResources)
			system.GET("/disk-usage", h.GetDiskUsage)
		}

		// 健康检查执行
		v1.POST("/health/check", h.ExecuteHealthCheck)

		// 短信服务（如果启用）
		if h.smsHandler != nil {
			sms := v1.Group("/sms")
			{
				sms.GET("/send", h.smsHandler.SendSMS)
				sms.POST("/send", h.smsHandler.SendSMS)
				sms.GET("/status", h.smsHandler.GetStatus)
			}
		}
	}

	// 邮件服务路由（独立路由组，匹配接口文档规范）
	if h.emailHandler != nil {
		email := r.Group("/email-endpoint")
		{
			email.POST("/send-simple", h.emailHandler.SendEmail)
			email.GET("/record/:id", h.emailHandler.GetEmailRecord)
			email.GET("/records", h.emailHandler.GetAllEmailRecords)
			email.DELETE("/records", h.emailHandler.ClearEmailRecords)
		}
	}

	return r
}

// HealthCheck Agent健康检查
func (h *Handler) HealthCheck(c *gin.Context) {
	response := gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"service":   "monitor-agent",
		"version":   "1.0.0",
	}

	// 检查Docker连接状态
	if h.dockerClient != nil {
		response["docker"] = "available"
	} else {
		response["docker"] = "unavailable"
	}

	c.JSON(http.StatusOK, response)
}

// RestartContainer 重启容器
func (h *Handler) RestartContainer(c *gin.Context) {
	if h.dockerClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Docker客户端不可用",
			"code":  "DOCKER_UNAVAILABLE",
		})
		return
	}

	containerName := c.Param("name")
	if containerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "容器名称是必需的",
			"code":  "MISSING_CONTAINER_NAME",
		})
		return
	}

	logger.Info("重启容器", "container", containerName, "client_ip", c.ClientIP())

	err := h.dockerClient.RestartContainer(containerName)
	if err != nil {
		logger.Error("重启容器失败", "container", containerName, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "重启容器失败",
			"details": err.Error(),
			"code":    "RESTART_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "容器重启成功",
		"container": containerName,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetContainerStatus 获取容器状态
func (h *Handler) GetContainerStatus(c *gin.Context) {
	if h.dockerClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Docker客户端不可用",
			"code":  "DOCKER_UNAVAILABLE",
		})
		return
	}

	containerName := c.Param("name")
	if containerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "容器名称是必需的",
			"code":  "MISSING_CONTAINER_NAME",
		})
		return
	}

	status, err := h.dockerClient.GetContainerStatus(containerName)
	if err != nil {
		logger.Error("获取容器状态失败", "container", containerName, "error", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取容器状态失败",
			"details": err.Error(),
			"code":    "STATUS_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": status,
	})
}

// GetContainerLogs 获取容器日志
func (h *Handler) GetContainerLogs(c *gin.Context) {
	if h.dockerClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Docker客户端不可用",
			"code":  "DOCKER_UNAVAILABLE",
		})
		return
	}

	containerName := c.Param("name")
	if containerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "容器名称是必需的",
			"code":  "MISSING_CONTAINER_NAME",
		})
		return
	}

	linesStr := c.DefaultQuery("lines", "100")
	lines, err := strconv.Atoi(linesStr)
	if err != nil {
		lines = 100
	}

	logs, err := h.dockerClient.GetContainerLogs(containerName, lines)
	if err != nil {
		logger.Error("获取容器日志失败", "container", containerName, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取容器日志失败",
			"details": err.Error(),
			"code":    "LOGS_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"container": containerName,
		"lines":     len(logs),
		"logs":      logs,
	})
}

// ListContainers 列出所有容器
func (h *Handler) ListContainers(c *gin.Context) {
	if h.dockerClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Docker客户端不可用",
			"code":  "DOCKER_UNAVAILABLE",
		})
		return
	}

	containers, err := h.dockerClient.ListContainers()
	if err != nil {
		logger.Error("列出容器失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "列出容器失败",
			"details": err.Error(),
			"code":    "LIST_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  containers,
		"count": len(containers),
	})
}

// GetSystemInfo 获取系统信息
func (h *Handler) GetSystemInfo(c *gin.Context) {
	info, err := h.systemCollector.GetSystemInfo()
	if err != nil {
		logger.Error("获取系统信息失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取系统信息失败",
			"details": err.Error(),
			"code":    "SYSTEM_INFO_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": info,
	})
}

// GetSystemResources 获取系统资源
func (h *Handler) GetSystemResources(c *gin.Context) {
	resources, err := h.systemCollector.GetSystemResources()
	if err != nil {
		logger.Error("获取系统资源失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取系统资源失败",
			"details": err.Error(),
			"code":    "SYSTEM_RESOURCES_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resources,
	})
}

// GetDiskUsage 获取磁盘使用情况分析
func (h *Handler) GetDiskUsage(c *gin.Context) {
	// 获取查询参数
	includeDirAnalysis := c.DefaultQuery("include_dirs", "false") == "true"
	maxTopDirs := 5
	if dirsStr := c.Query("max_dirs"); dirsStr != "" {
		if val, err := strconv.Atoi(dirsStr); err == nil && val > 0 {
			maxTopDirs = val
		}
	}

	// 获取磁盘使用分析
	analysis, err := h.systemCollector.GetDiskUsageAnalysis(includeDirAnalysis, maxTopDirs)
	if err != nil {
		logger.Error("获取磁盘使用分析失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取磁盘使用分析失败",
			"details": err.Error(),
			"code":    "DISK_USAGE_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": analysis,
	})
}

// HealthCheckRequest 健康检查请求
type HealthCheckRequest struct {
	Endpoint string `json:"endpoint" binding:"required"`
	Method   string `json:"method"`
	Timeout  int    `json:"timeout"`
}

// ExecuteHealthCheck 执行健康检查
func (h *Handler) ExecuteHealthCheck(c *gin.Context) {
	var req HealthCheckRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的请求体",
			"details": err.Error(),
			"code":    "INVALID_REQUEST",
		})
		return
	}

	// 设置默认值
	if req.Method == "" {
		req.Method = "GET"
	}
	if req.Timeout == 0 {
		req.Timeout = 10
	}

	logger.Info("Executing health check",
		"endpoint", req.Endpoint,
		"method", req.Method,
		"timeout", req.Timeout,
		"client_ip", c.ClientIP())

	// 执行健康检查
	start := time.Now()
	success, statusCode, err := h.executeHTTPCheck(req.Endpoint, req.Method, req.Timeout)
	duration := time.Since(start)

	result := gin.H{
		"endpoint":      req.Endpoint,
		"method":        req.Method,
		"success":       success,
		"status_code":   statusCode,
		"response_time": duration.Milliseconds(),
		"timestamp":     time.Now().Format(time.RFC3339),
	}

	if err != nil {
		result["error"] = err.Error()
	}

	if success {
		c.JSON(http.StatusOK, gin.H{"data": result})
	} else {
		c.JSON(http.StatusOK, gin.H{"data": result}) // 仍然返回200，但在data中标记失败
	}
}

// executeHTTPCheck 执行HTTP健康检查
func (h *Handler) executeHTTPCheck(endpoint, method string, timeout int) (bool, int, error) {
	client := &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, method, endpoint, nil)
	if err != nil {
		return false, 0, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		return false, 0, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 2xx状态码认为是成功
	success := resp.StatusCode >= 200 && resp.StatusCode < 300
	return success, resp.StatusCode, nil
}
