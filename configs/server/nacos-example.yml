# Monitor系统完整配置实例 - 包含各种可能的配置情况
# Data ID: monitor
# Group: DEFAULT_GROUP
#
# 本配置文件展示了Monitor系统支持的所有配置选项和各种使用场景
# 实际使用时可根据需要选择相应的配置项

monitor:
  # ==================== 基础服务配置 ====================

  # HTTP服务器配置
  server:
    port: 8080                             # 监控服务端口

  # 数据库配置
  database:
    host: *************                    # 数据库主机
    port: 5432                             # 数据库端口
    database: vdm                          # 数据库名
    username: vdm                          # 数据库用户名
    password: vDm#2023@postGre             # 数据库密码
    schema: monitor                        # 数据库模式
    max_connections: 20                    # 最大连接数
    ssl_mode: disable                      # SSL模式：disable/require/verify-ca/verify-full

  # 日志配置
  logging:
    level: "debug"                         # 日志级别：debug/info/warn/error/fatal
    format: "console"                      # 日志格式：console/json

  # ==================== 全局默认配置 ====================

  # 全局Agent默认配置（所有使用Agent的服务共享）
  agent_defaults:
    port: 9090                             # 统一Agent端口
    token: "secure-token-123"              # 统一Agent认证token
    timeout: 30                            # 连接超时时间（秒）
    retry_count: 3                         # 重试次数

  # 监控默认配置（所有服务共享的监控参数）
  monitor_defaults:
    type: "http"                           # 默认监控类型：http/kafka/emqx/business_api
    method: "GET"                          # 默认HTTP方法：GET/POST/PUT/DELETE
    check_interval: 30                     # 默认检查间隔（秒）
    timeout: 10                            # 默认超时时间（秒）
    retry_count: 3                         # 默认重试次数
    expected_status: [200, 201, 204]       # 默认期望的HTTP状态码
    health_path: "/health/check"        # 默认健康检查路径
    # 网段过滤配置（可选）- 只监控指定网段的服务实例
    allowed_networks:                      # 允许监控的网段列表，支持CIDR和通配符格式
      - "192.168.1.*"                      # 通配符格式：监控192.168.1.x网段
      - "10.0.0.0/8"                       # CIDR格式：监控10.x.x.x网段
      # - "**********/12"                  # 可以添加更多网段
      # - "*************"                  # 也可以指定单个IP
      # 如果不配置此项，则允许所有网段的服务实例
    docker:                                # Docker默认配置
      restart_policy: "unless-stopped"    # 默认重启策略：no/always/unless-stopped/on-failure
      max_restart_count: 5                 # 默认最大重启次数
      restart_cooldown: "5m"               # 默认重启冷却时间

  # ==================== 自动解决配置 ====================

  # 自动解决配置（告警自动恢复）
  auto_resolve:
    enabled: true                          # 启用告警自动恢复
    healthy_checks_required: 2             # HTTP服务需要连续多少次健康检查通过才自动恢复告警
    kafka_recovery_threshold: 80           # Kafka延迟恢复阈值百分比（0-100），延迟低于阈值的这个百分比时自动恢复
    emqx_recovery_threshold: 80            # EMQX速率恢复阈值百分比（0-100），速率恢复到基线的这个百分比时自动恢复告警
    send_notification: false               # 是否发送恢复通知

  # ==================== 服务监控配置 ====================

  # 服务配置 - 展示各种监控类型和配置场景
  services:
    # ==================== HTTP服务监控示例 ====================

    # 1. 最简HTTP服务配置（使用Nacos服务发现）
    - name: "vdm-alarm"                    # Nacos中的服务名
      docker:                              # Docker容器配置
        container_name: "vdm-alarm"        # Docker容器名

    # 1.1. 多实例HTTP服务配置（自动发现所有实例）
    - name: "vdm-user-service"             # Nacos中的服务名
      check_interval: 30                   # 检查间隔
      timeout: 10                          # 超时时间
      docker:                              # Docker容器配置
        container_name: "vdm-user-service" # Docker容器名
        restart_policy: "unless-stopped"   # 重启策略
        agent:                             # 使用Agent管理容器
          host: "*************"            # Agent主机

    # 2. 完整HTTP服务配置（手动指定endpoint）
    - name: "vdm-gateway"
      type: "http"                         # 明确指定监控类型
      endpoint: "http://*************:8080/health"  # 手动指定健康检查地址
      method: "GET"                        # HTTP方法
      check_interval: 30                   # 检查间隔
      timeout: 15                          # 超时时间
      retry_count: 3                       # 重试次数
      expected_status: [200, 201]          # 期望状态码
      docker:
        container_name: "vdm-gateway"
        restart_policy: "always"           # 重启策略
        max_restart_count: 10               # 最大重启次数
        restart_cooldown: "3m"              # 重启冷却时间
        agent:                              # 使用远程Agent
          host: "*************"             # Agent主机
          port: 9090                        # Agent端口（可选，使用默认）
          token: "custom-token-456"         # 自定义token（可选，使用默认）

    # 3. 业务API监控（带认证）
    - name: "user-api"
      type: "business_api"                 # 业务API类型
      endpoint: "http://*************:8080/api/v1/users/health"
      method: "POST"                       # POST请求
      check_interval: 60                   # 较长检查间隔
      timeout: 30                          # 较长超时时间
      auth:                                # 认证配置
        username: "monitor"                # API用户名
        password: "monitor123"             # API密码
      thresholds:                          # 自定义阈值
        response_time: 5000                # 响应时间阈值（毫秒）
        cpu_usage: 80                      # CPU使用率阈值
        memory_usage: 85                   # 内存使用率阈值
      docker:
        container_name: "user-service"
        agent:
          host: "*************"

    # ==================== Kafka集群监控示例 ====================

    # 4. Kafka集群监控（多broker、多消费者组）
    - name: "kafka-cluster"
      type: "kafka"
      check_interval: 60                   # Kafka检查间隔通常较长
      kafka:
        brokers:                           # 多个broker地址
          - "*************:9092"
          - "*************:9092"
          - "*************:9092"
        consumer_groups:                   # 多个消费者组
          - "location_send_group"
          - "alarm_process_group"
          - "data_sync_group"
        version: "2.8.0"                   # Kafka版本
        timeout: 30                        # 连接超时
        retry_count: 3                     # 重试次数
        auth:                              # SASL认证（可选）
          mechanism: "PLAIN"               # 认证机制
          username: "kafka_user"           # 用户名
          password: "kafka_pass"           # 密码
      thresholds:                          # Kafka特定阈值
        topic_lag: 1000                    # 主题延迟阈值
        partition_lag: 500                 # 分区延迟阈值
        consume_rate: 100                  # 消费速率阈值

    # ==================== EMQX/MQTT监控示例 ====================

    # 5. EMQX监控（单主题）
    - name: "emqx-simple"
      type: "emqx"
      check_interval: 30
      emqx:
        host: "*************"              # EMQX服务器地址
        port: 1883                         # MQTT端口
        topics:                            # 监控的主题列表
          - "device/data/+/status"         # 设备状态主题（支持通配符）
        rate_window: 300                   # 速率计算窗口（5分钟）
        baseline_window: 1800              # 基线计算窗口（30分钟）
        min_baseline_rate: 5               # 最小基线速率保护
        auth:                              # MQTT认证
          username: "monitor"              # MQTT用户名
          password: "monitor123"           # MQTT密码
        timeout: 30                        # 连接超时
        retry_count: 3                     # 重试次数
      thresholds:                          # EMQX特定阈值
        emqx_rate_drop_threshold: 50       # 速率下降阈值（百分比）
      docker:
        container_name: "emqx-server"
        agent:
          host: "*************"

    # 6. EMQX监控（多主题、高级配置）
    - name: "emqx-advanced"
      type: "emqx"
      check_interval: 60                   # 较长检查间隔
      emqx:
        host: "*************"
        port: 1883
        topics:                            # 多个主题监控
          - "vehicle/location/+/+/data"    # 车辆位置数据
          - "vehicle/alarm/+/+/alert"      # 车辆告警数据
          - "device/heartbeat/+/ping"      # 设备心跳数据
        rate_window: 600                   # 10分钟速率窗口
        baseline_window: 3600              # 1小时基线窗口
        min_baseline_rate: 10              # 更高的最小基线速率
        auth:
          username: "admin"
          password: "emqX@2025"
        timeout: 45                        # 更长超时时间
        retry_count: 5                     # 更多重试次数
      docker:
        container_name: "emqx-cluster"
        restart_policy: "on-failure"       # 失败时重启
        max_restart_count: 3
        restart_cooldown: "10m"
        agent:
          host: "*************"
          timeout: 60                      # Agent超时时间
          retry_count: 5                   # Agent重试次数

    # ==================== 多实例监控高级示例 ====================

    # 8. 多实例服务监控（带自定义配置）
    - name: "api-gateway"                  # 微服务网关
      type: "http"                         # 监控类型
      method: "GET"                        # HTTP方法
      check_interval: 20                   # 较短检查间隔
      timeout: 8                           # 较短超时时间
      retry_count: 2                       # 较少重试次数
      expected_status: [200, 204]          # 期望状态码
      health_path: "/actuator/health"      # 健康检查路径
      thresholds:                          # 自定义阈值
        response_time: 3000                # 响应时间阈值（毫秒）
        cpu_usage: 75                      # CPU使用率阈值
        memory_usage: 80                   # 内存使用率阈值
      docker:
        container_name: "api-gateway"      # 容器名
        restart_policy: "always"           # 重启策略
        max_restart_count: 8               # 最大重启次数
        restart_cooldown: "2m"             # 重启冷却时间
        agent:
          host: "*************"            # Agent主机
          port: 9090                       # Agent端口
          token: "gateway-agent-token"     # 专用token

    # 9. 混合模式服务监控（部分实例多实例，部分单实例）
    - name: "order-service"                # 订单服务
      check_interval: 45                   # 检查间隔
      timeout: 12                          # 超时时间
      docker:
        container_name: "order-service"
        agent:
          host: "*************"

    # ==================== 混合服务监控示例 ====================

    # 7. 多Agent环境示例
    - name: "distributed-service"
      type: "http"
      endpoint: "http://*************:8080/health"
      check_interval: 45
      docker:
        container_name: "distributed-app"
        agent:
          host: "*************"            # 专用Agent主机
          port: 9091                       # 非标准Agent端口
          token: "agent-107-token"         # 专用Agent token
          timeout: 45                      # 专用超时配置
          retry_count: 4                   # 专用重试配置


  # ==================== 告警规则配置 ====================

  # 告警规则配置 - 展示各种告警类型和触发条件
  alert_rules:
    # 1. HTTP服务连续失败告警（支持自动重启）
    - name: "http_service_consecutive_failures"
      services: ["vdm-alarm", "vdm-gateway", "user-api"]  # 适用的服务列表
      condition:
        type: "consecutive_failures"       # 告警类型：连续失败
        threshold: 3                       # 连续失败3次触发告警
      severity: "critical"                 # 告警级别：critical/warning/info
      enabled: true                        # 启用此告警规则
      auto_restart_container: true         # 告警触发时自动重启容器
      restart_container_name: "vdm-alarm" # 指定要重启的容器名称（可选）

    # 2. Kafka消费延迟告警（主题级别）
    - name: "kafka_topic_lag_high"
      services: ["kafka-cluster"]          # 适用于Kafka服务
      condition:
        type: "topic_lag"                  # 告警类型：主题延迟
        threshold: 1000                    # 延迟阈值
      severity: "warning"
      enabled: true
      auto_restart_container: true

    # 3. Kafka分区延迟告警（分区级别）
    - name: "kafka_partition_lag_critical"
      services: ["kafka-cluster"]
      condition:
        type: "partition_lag"              # 告警类型：分区延迟
        threshold: 500                     # 分区延迟阈值
      severity: "critical"
      enabled: true
      auto_restart_container: false        # 不自动重启，需要人工干预

    # 4. EMQX消息速率下降告警
    - name: "emqx_message_rate_drop"
      services: ["emqx-simple", "emqx-advanced"]
      condition:
        type: "emqx_rate_drop"             # 告警类型：EMQX速率下降
        threshold: 50                      # 速率下降50%触发告警
      severity: "warning"
      enabled: true
      auto_restart_container: true
      restart_container_name: "emqx-server"

    # 5. 系统资源告警（CPU使用率）
    - name: "high_cpu_usage"
      services: ["vdm-gateway", "user-api"] # 适用于高负载服务
      condition:
        type: "cpu_usage"                  # 告警类型：CPU使用率
        threshold: 85                      # CPU使用率超过85%
      severity: "warning"
      enabled: true
      auto_restart_container: false        # CPU高不一定需要重启

    # 6. 系统资源告警（内存使用率）
    - name: "high_memory_usage"
      services: ["kafka-cluster", "emqx-advanced"]
      condition:
        type: "memory_usage"               # 告警类型：内存使用率
        threshold: 90                      # 内存使用率超过90%
      severity: "critical"
      enabled: true
      auto_restart_container: true         # 内存泄漏可能需要重启

    # 7. 响应时间告警
    - name: "slow_response_time"
      services: ["user-api", "vdm-gateway"]
      condition:
        type: "response_time"              # 告警类型：响应时间
        threshold: 5000                    # 响应时间超过5秒
      severity: "warning"
      enabled: true
      auto_restart_container: false        # 响应慢不一定需要重启

    # 8. 磁盘使用率告警
    - name: "high_disk_usage"
      services: ["kafka-cluster"]          # 主要针对存储密集型服务
      condition:
        type: "disk_usage"                 # 告警类型：磁盘使用率
        threshold: 80                      # 磁盘使用率超过80%
      severity: "warning"
      enabled: true
      auto_restart_container: false        # 磁盘满不能通过重启解决

    # 9. 连接数激增告警
    - name: "connection_spike"
      services: ["emqx-simple", "emqx-advanced"]
      condition:
        type: "connection_spike"           # 告警类型：连接数激增
        threshold: 1000                    # 连接数超过1000
      severity: "info"                     # 信息级别告警
      enabled: true
      auto_restart_container: false

    # Kafka分区消费延迟告警规则（支持容器重启）
    - name: "kafka_partition_lag_high"
      services: ["kafka"]
      condition:
        type: "partition_lag"
        threshold: 50
      severity: "warning"
      enabled: true
      auto_restart_container: true        # 告警触发时自动重启容器

    # EMQX主题速率下降告警规则（支持容器重启，指定特定容器）
    - name: "emqx_topic_rate_drop"
      services: ["emqx"]  # 指定适用的服务
      condition:
        type: "emqx_rate_drop"
        threshold: 30             # 当前速率低于基线的30%时触发告警
      severity: "warning"
      enabled: true
      auto_restart_container: true        # 告警触发时自动重启容器
      restart_container_name: "vdm-inter-manager"  # 指定要重启的容器名称


  # ==================== 通知配置 ====================

  # 通知配置 - 展示各种通知渠道和策略
  notifications:
    # 通知渠道配置
    channels:
      # 邮件通知配置
      email:
        # HTTP邮件接口配置（推荐方式）
        url: "http://*************:11003/email-endpoint/send-simple"
        timeout_seconds: 30                # HTTP请求超时时间

        # SMTP配置（传统方式，可选）
        smtp_host: "smtp.company.com"      # SMTP服务器地址
        smtp_port: 587                     # SMTP端口
        username: "<EMAIL>"    # SMTP用户名
        password: "smtp_password"          # SMTP密码
        tls_enabled: true                  # 启用TLS

      # 短信配置
      sms:
        url: "http://*************:9090/api/v1/sms/send"  # 短信接口地址
        username: "sms_user"               # 短信接口用户名
        password: "sms_password"           # 短信接口密码
        timeout_seconds: 30                # 请求超时时间

      # Webhook通知配置
      webhook:
        url: "http://*************:8080/api/alerts/webhook"  # Webhook地址
        timeout: 15                        # 超时时间
        retry_count: 3                     # 重试次数

    # ==================== 通知分组配置 ====================

    # 通知分组 - 按团队、服务类型、告警级别分组
    groups:
      # 1. 后端开发团队
      - name: "backend-team"
        services: ["vdm-alarm", "vdm-gateway", "user-api"]  # 负责的服务
        channels: ["email", "sms"]         # 使用的通知渠道
        recipients:                        # 接收人配置
          email:
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
          sms:
            - "13800138000"                # 团队负责人
            - "13900139000"                # 值班人员
        escalation:                        # 告警升级策略
          - level: 1                       # 第一级：立即通知
            delay: "0m"
            channels: ["email"]
          - level: 2                       # 第二级：15分钟后升级
            delay: "15m"
            channels: ["email", "sms"]
          - level: 3                       # 第三级：1小时后最高级升级
            delay: "60m"
            channels: ["sms"]
            recipients:                    # 升级到更高级别人员
              sms: ["13700137000"]         # 技术总监

      # 2. 基础设施团队
      - name: "infrastructure-team"
        services: ["kafka-cluster", "emqx-simple", "emqx-advanced"]
        channels: ["email", "webhook", "sms"]
        recipients:
          email:
            - "<EMAIL>"
            - "<EMAIL>"
          sms:
            - "13600136000"
            - "13500135000"
        escalation:
          - level: 1
            delay: "0m"
            channels: ["webhook"]          # 首先通知监控系统
          - level: 2
            delay: "10m"
            channels: ["email"]
          - level: 3
            delay: "30m"
            channels: ["sms"]

      # 3. 运维团队（24小时值班）
      - name: "ops-team"
        services: ["distributed-service"] # 分布式服务
        channels: ["sms", "webhook"]       # 快速响应渠道
        recipients:
          sms:
            - "***********"                # 值班手机
            - "***********"                # 备用值班
        escalation:
          - level: 1
            delay: "0m"
            channels: ["sms"]
          - level: 2
            delay: "5m"                    # 快速升级
            channels: ["sms", "webhook"]

      # 4. 业务团队（工作时间）
      - name: "business-team"
        services: ["user-api"]             # 业务相关服务
        channels: ["email"]                # 主要使用邮件
        recipients:
          email:
            - "<EMAIL>"
            - "<EMAIL>"
        escalation:
          - level: 1
            delay: "0m"
            channels: ["email"]

      # 5. 全局告警组（紧急情况）
      - name: "emergency-team"
        services: ["*"]                    # 所有服务
        channels: ["sms"]                  # 仅短信
        recipients:
          sms:
            - "***********"                # 技术总监
            - "***********"                # CTO
        escalation:
          - level: 1
            delay: "0m"
            channels: ["sms"]

    # 告警抑制配置（简化版）
    suppression:
      rules: []                            # 抑制规则列表（可根据需要添加）

# ==================== 配置最佳实践说明 ====================
#
# 1. 服务配置最佳实践：
#    - 使用Nacos服务发现可以简化配置，只需指定服务名和容器名
#    - 对于关键服务，建议手动指定endpoint以确保监控准确性
#    - 根据服务重要性设置不同的检查间隔和超时时间
#    - 为不同类型的服务设置合适的阈值
#
# 1.1. 多实例监控最佳实践：
#    - 系统默认启用服务发现功能，自动从Nacos发现所有HTTP服务
#    - HTTP服务默认启用多实例监控，无需手动配置
#    - 所有HTTP服务默认启用多实例监控，无需额外配置
#    - 多实例模式下不要手动配置endpoint，系统会自动从Nacos获取所有实例
#    - 基础设施服务（kafka、emqx）不支持多实例监控
#    - 建议为多实例服务设置较短的检查间隔以快速发现问题
#    - 多实例服务的容器重启会影响所有实例，请谨慎配置自动重启
#    - 所有服务（包括自动发现的）都会进行预检查
#
# 1.2. 网段过滤最佳实践：
#    - 使用allowed_networks配置只监控指定网段的服务实例
#    - 支持通配符格式（192.168.1.*）、CIDR格式（***********/24）和单个IP
#    - 建议明确指定内网段，避免监控外网或不可访问的服务实例
#    - 网段过滤只对自动发现的服务生效，手动配置的endpoint不受影响
#    - 如果不配置allowed_networks，则允许所有网段的服务实例
#
# 2. Docker容器管理最佳实践：
#    - 使用Agent代理可以实现跨主机容器管理
#    - 设置合理的重启策略和冷却时间，避免频繁重启
#    - 为高可用服务设置较高的最大重启次数
#    - 在生产环境中谨慎使用自动重启功能
#
# 3. 告警规则最佳实践：
#    - 根据服务的SLA设置合适的告警阈值
#    - 为不同级别的告警设置不同的处理策略
#    - 定期审查和调整告警规则，减少误报
#    - 合理使用自动重启容器功能，避免过度依赖
#
# 4. 通知配置最佳实践：
#    - 使用分组和升级策略确保告警及时处理
#    - 为不同团队配置不同的通知渠道
#    - 设置合理的升级延迟时间
#    - 确保关键人员能及时收到严重告警
#
# 5. 性能优化建议：
#    - 根据服务负载调整检查间隔，避免过于频繁的检查
#    - 使用连接池和复用连接减少网络开销
#    - 定期清理历史数据，保持数据库性能
#    - 监控Monitor系统自身的资源使用情况
#
# 6. 安全配置建议：
#    - 使用强密码和token进行认证
#    - 在生产环境中启用TLS加密
#    - 定期轮换密码和token
#    - 限制网络访问，使用防火墙规则
#
# 7. 环境变量覆盖：
#    - 敏感信息（如密码、token）建议使用环境变量覆盖
#    - 支持的环境变量格式：MONITOR_DATABASE_PASSWORD、MONITOR_AGENT_DEFAULTS_TOKEN等
#    - 环境变量优先级高于配置文件
#
# ==================== 配置文件版本信息 ====================
#
# 配置文件版本: v2.2.0
# 适用系统版本: Monitor System 1.0.0+
# 最后更新时间: 2025-07-09
# 维护团队: Monitor Development Team
#
# 更新日志:
# v3.0.0 (2025-07-09):
#   - 移除service_discovery配置块，简化配置
#   - 系统默认启用自动服务发现功能
#   - HTTP服务默认启用多实例监控
#   - 所有服务默认启用预检查
#   - 大幅简化配置复杂度，提升用户体验
#   - 保持向后兼容性，支持显式禁用多实例监控
#
# v2.1.0 (2025-01-07):
#   - 修正配置字段与代码结构的匹配性
#   - 移除不支持的日志轮转和服务发现高级配置
#   - 简化通知配置结构，确保与代码实现一致
#   - 修正Kafka和EMQX配置字段名称
#   - 移除告警规则中不支持的cooldown字段
#   - 更新最佳实践说明
#
# v2.0.0 (2025-01-07):
#   - 添加完整的配置示例，覆盖所有监控类型
#   - 增加高级告警规则和通知策略
#   - 添加多Agent环境支持
#   - 完善安全和性能配置选项
#   - 增加配置最佳实践说明
#
# v1.0.0 (2024-12-01):
#   - 初始版本，基础HTTP、Kafka、EMQX监控配置
#
# ==================== 技术支持 ====================
#
# 如需技术支持，请提供：
# 1. 系统版本信息
# 2. 完整的错误日志
# 3. 相关的配置片段
# 4. 网络环境信息
#
# 联系方式：
# - 邮箱: <EMAIL>
# - 文档: https://docs.company.com/monitor
# - 问题跟踪: https://issues.company.com/monitor
#
# ==================== 配置文件结束 ====================
