# Monitor Agent 配置文件

# HTTP服务器配置
server:
  host: "0.0.0.0"
  port: 9090

# 安全配置
security:
  # API访问令牌，建议使用环境变量 AGENT_TOKEN 覆盖
  token: "secure-token-123"
  # 是否启用TLS
  enable_tls: false
  # TLS证书文件路径（启用TLS时必需）
  cert_file: ""
  # TLS私钥文件路径（启用TLS时必需）
  key_file: ""

# Docker配置
docker:
  # Docker socket路径
  socket_path: "/var/run/docker.sock"
  # Docker操作超时时间（秒）
  timeout: 30

# 系统监控配置
system:
  # 系统信息收集间隔（秒）
  collect_interval: 10

# 日志配置
logging:
  # 日志级别: debug, info, warn, error, fatal
  level: "info"
  # 日志格式: json, console
  format: "json"

# SMS配置
sms:
  # 是否启用短信功能
  enabled: false
